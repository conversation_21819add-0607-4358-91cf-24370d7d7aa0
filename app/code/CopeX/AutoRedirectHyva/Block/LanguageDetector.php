<?php
/**
 * CopeX AutoRedirectHyva Language Detector Block
 *
 * @category  CopeX
 *
 * @package   CopeX_AutoRedirectHyva
 *
 * <AUTHOR> Team
 *
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\AutoRedirectHyva\Block;

use CopeX\AutoRedirectHyva\Helper\LanguageDetection;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;

class LanguageDetector extends Template
{
    private LanguageDetection $languageDetectionHelper;

    private StoreManagerInterface $storeManager;

    private Json $jsonSerializer;

    public function __construct(
        Context $context,
        LanguageDetection $languageDetectionHelper,
        StoreManagerInterface $storeManager,
        Json $jsonSerializer,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->languageDetectionHelper = $languageDetectionHelper;
        $this->storeManager = $storeManager;
        $this->jsonSerializer = $jsonSerializer;
    }

    /**
     * Check if language detection should be performed
     */
    public function shouldPerformDetection(): bool
    {
        return $this->languageDetectionHelper->isEnabled() &&
               ! $this->languageDetectionHelper->isLanguageDetected();
    }

    /**
     * Check if debug mode is enabled
     */
    public function isDebugEnabled(): bool
    {
        return $this->languageDetectionHelper->isDebugEnabled();
    }

    /**
     * Get available stores with language mapping
     *
     * @return string JSON encoded array of stores
     */
    public function getAvailableStoresJson(): string
    {
        $stores = [];

        try {
            foreach ($this->storeManager->getStores() as $store) {
                $locale = $store->getConfig('general/locale/code');
                $language = substr($locale, 0, 2);

                $stores[] = [
                    'id' => $store->getId(),
                    'code' => $store->getCode(),
                    'name' => $store->getName(),
                    'language' => $language,
                    'locale' => $locale,
                    'base_url' => $store->getBaseUrl(),
                ];
            }
        } catch (\Exception $e) {
            // Return empty array on error
            return '[]';
        }

        return $this->jsonSerializer->serialize($stores);
    }

    /**
     * Get current store information
     *
     * @return string JSON encoded current store info
     */
    public function getCurrentStoreJson(): string
    {
        try {
            $currentStore = $this->storeManager->getStore();
            $locale = $currentStore->getConfig('general/locale/code');
            $language = substr($locale, 0, 2);

            $storeInfo = [
                'id' => $currentStore->getId(),
                'code' => $currentStore->getCode(),
                'name' => $currentStore->getName(),
                'language' => $language,
                'locale' => $locale,
                'base_url' => $currentStore->getBaseUrl(),
            ];

            return $this->jsonSerializer->serialize($storeInfo);
        } catch (\Exception $e) {
            return '{}';
        }
    }

    /**
     * Get cookie name for language detection
     */
    public function getCookieName(): string
    {
        return LanguageDetection::COOKIE_NAME;
    }

    /**
     * Get language mapping configuration
     *
     * @return string JSON encoded language mapping
     */
    public function getLanguageMappingJson(): string
    {
        $mapping = $this->languageDetectionHelper->getLanguageMapping();
        return $this->jsonSerializer->serialize($mapping);
    }
}
