<?php
/**
 * CopeX AutoRedirectHyva Language Detection Helper
 *
 * @category  CopeX
 *
 * @package   CopeX_AutoRedirectHyva
 *
 * <AUTHOR> Team
 *
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\AutoRedirectHyva\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\HTTP\Header;
use Magento\Framework\Session\SessionManagerInterface;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Magento\Framework\Stdlib\CookieManagerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class LanguageDetection extends AbstractHelper
{
    public const COOKIE_NAME = 'copex_language_detected';
    public const CONFIG_PATH_ENABLED = 'copex_autoredirect/general/enabled';
    public const CONFIG_PATH_DEBUG = 'copex_autoredirect/general/debug';
    public const CONFIG_PATH_COOKIE_LIFETIME = 'copex_autoredirect/general/cookie_lifetime';
    public const CONFIG_PATH_GERMAN_LANGUAGES = 'copex_autoredirect/language_mapping/german_languages';
    public const CONFIG_PATH_ENGLISH_LANGUAGES = 'copex_autoredirect/language_mapping/english_languages';

    private StoreManagerInterface $storeManager;

    private Header $httpHeader;

    private CookieManagerInterface $cookieManager;

    private CookieMetadataFactory $cookieMetadataFactory;

    private SessionManagerInterface $sessionManager;

    private LoggerInterface $logger;

    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        Header $httpHeader,
        CookieManagerInterface $cookieManager,
        CookieMetadataFactory $cookieMetadataFactory,
        SessionManagerInterface $sessionManager,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->storeManager = $storeManager;
        $this->httpHeader = $httpHeader;
        $this->cookieManager = $cookieManager;
        $this->cookieMetadataFactory = $cookieMetadataFactory;
        $this->sessionManager = $sessionManager;
        $this->logger = $logger;
    }

    /**
     * Check if language detection has already been performed
     */
    public function isLanguageDetected(): bool
    {
        return (bool) $this->cookieManager->getCookie(self::COOKIE_NAME);
    }

    /**
     * Check if module is enabled
     */
    public function isEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::CONFIG_PATH_ENABLED,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Check if debug mode is enabled
     */
    public function isDebugEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::CONFIG_PATH_DEBUG,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get cookie lifetime in seconds
     */
    public function getCookieLifetime(): int
    {
        $days = (int) $this->scopeConfig->getValue(
            self::CONFIG_PATH_COOKIE_LIFETIME,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        return $days > 0 ? $days * 86400 : 86400 * 30; // Default 30 days
    }

    /**
     * Mark language detection as completed
     */
    public function markLanguageDetected(): void
    {
        $metadata = $this->cookieMetadataFactory->createPublicCookieMetadata()
            ->setDuration($this->getCookieLifetime())
            ->setPath('/')
            ->setHttpOnly(false);

        $this->cookieManager->setPublicCookie(
            self::COOKIE_NAME,
            '1',
            $metadata
        );
    }

    /**
     * Get browser language from Accept-Language header
     */
    public function getBrowserLanguage(): ?string
    {
        $acceptLanguage = $this->httpHeader->getHttpAcceptLanguage();

        if (! $acceptLanguage) {
            return null;
        }

        // Parse Accept-Language header
        $languages = [];
        $parts = explode(',', $acceptLanguage);

        foreach ($parts as $part) {
            $part = trim($part);
            if (strpos($part, ';') !== false) {
                [$lang, $quality] = explode(';', $part, 2);
                $quality = (float) str_replace('q=', '', $quality);
            } else {
                $lang = $part;
                $quality = 1.0;
            }

            $languages[trim($lang)] = $quality;
        }

        // Sort by quality (preference)
        arsort($languages);

        // Find the best matching language
        foreach ($languages as $browserLang => $quality) {
            $detectedLang = $this->mapBrowserLanguageToStoreLanguage($browserLang);
            if ($detectedLang) {
                return $detectedLang;
            }
        }

        return null;
    }

    /**
     * Get language mapping from configuration
     */
    public function getLanguageMapping(): array
    {
        $germanLanguages = $this->scopeConfig->getValue(
            self::CONFIG_PATH_GERMAN_LANGUAGES,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        $englishLanguages = $this->scopeConfig->getValue(
            self::CONFIG_PATH_ENGLISH_LANGUAGES,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        $mapping = [];

        if ($germanLanguages) {
            $mapping['de'] = array_filter(array_map('trim', explode("\n", $germanLanguages)));
        }

        if ($englishLanguages) {
            $mapping['en'] = array_filter(array_map('trim', explode("\n", $englishLanguages)));
        }

        // Fallback to default if empty
        if (empty($mapping)) {
            $mapping = [
                'de' => ['de', 'de-DE', 'de-AT', 'de-CH'],
                'en' => ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU'],
            ];
        }

        return $mapping;
    }

    /**
     * Get target store for detected language
     */
    public function getTargetStoreForLanguage(string $detectedLanguage): ?\Magento\Store\Api\Data\StoreInterface
    {
        try {
            $stores = $this->storeManager->getStores();

            foreach ($stores as $store) {
                $storeLocale = $this->scopeConfig->getValue(
                    'general/locale/code',
                    \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
                    $store->getId()
                );

                // Map store locale to language
                $storeLanguage = substr($storeLocale, 0, 2);

                if ($storeLanguage === $detectedLanguage) {
                    return $store;
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('Error getting target store for language: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Check if current request should be redirected
     */
    public function shouldRedirect(): bool
    {
        // Check if module is enabled
        if (! $this->isEnabled()) {
            return false;
        }

        // Don't redirect if language already detected
        if ($this->isLanguageDetected()) {
            return false;
        }

        // Don't redirect AJAX requests
        if ($this->_request->isXmlHttpRequest()) {
            return false;
        }

        // Don't redirect POST requests
        if ($this->_request->isPost()) {
            return false;
        }

        // Don't redirect admin or API requests
        $requestUri = $this->_request->getRequestUri();
        if (strpos($requestUri, '/admin') === 0 ||
            strpos($requestUri, '/rest/') === 0 ||
            strpos($requestUri, '/soap/') === 0) {
            return false;
        }

        return true;
    }

    /**
     * Log debug information
     */
    public function logDebug(string $message, array $context = []): void
    {
        $this->logger->debug('[CopeX_AutoRedirectHyva] ' . $message, $context);
    }

    /**
     * Map browser language to store language
     */
    private function mapBrowserLanguageToStoreLanguage(string $browserLang): ?string
    {
        $browserLang = strtolower($browserLang);
        $languageMapping = $this->getLanguageMapping();

        foreach ($languageMapping as $storeLang => $browserLanguages) {
            foreach ($browserLanguages as $supportedLang) {
                if (strpos($browserLang, strtolower($supportedLang)) === 0) {
                    return $storeLang;
                }
            }
        }

        return null;
    }
}
