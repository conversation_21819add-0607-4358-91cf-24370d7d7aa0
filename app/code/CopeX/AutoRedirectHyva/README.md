# CopeX AutoRedirectHyva Module

## Overview

The CopeX AutoRedirectHyva module automatically redirects users to the appropriate store view based on their browser language settings. This module is specifically designed for Hyva themes and supports German and English language detection.

## Features

- **Automatic Language Detection**: Detects browser language from `Accept-Language` header and `navigator.languages`
- **Smart Redirection**: Redirects users only on their first visit
- **Cookie-based Memory**: Remembers user preference to avoid repeated redirections
- **Configurable**: Admin panel configuration for enabling/disabling and customizing language mappings
- **Debug Mode**: Console logging for troubleshooting
- **Hyva Compatible**: Designed specifically for Hyva themes with Alpine.js integration

## Supported Languages

### Default Configuration
- **German**: `de`, `de-DE`, `de-AT`, `de-CH`
- **English**: `en`, `en-US`, `en-GB`, `en-CA`, `en-AU`

## Installation

1. Copy the module files to `app/code/CopeX/AutoRedirectHyva/`
2. Enable the module:
   ```bash
   php bin/magento module:enable CopeX_AutoRedirectHyva
   php bin/magento setup:upgrade
   php bin/magento cache:flush
   ```

## Configuration

Navigate to **Stores > Configuration > CopeX > Auto Redirect Hyva** in the admin panel.

### General Settings
- **Enable Auto Redirect**: Enable/disable the module
- **Enable Debug Mode**: Enable console logging for debugging
- **Cookie Lifetime**: How long to remember language detection (default: 30 days)

### Language Mapping
- **German Browser Languages**: Configure which browser languages redirect to German store
- **English Browser Languages**: Configure which browser languages redirect to English store

## How It Works

1. **First Visit Detection**: Module checks if user has visited before using a cookie
2. **Language Detection**: Analyzes browser's `Accept-Language` header and JavaScript `navigator.languages`
3. **Store Matching**: Maps detected language to available store views
4. **Smart Redirect**: Redirects only if target store differs from current store
5. **Memory**: Sets cookie to prevent future automatic redirections

## Technical Details

### Observer
- **Event**: `controller_action_predispatch`
- **Class**: `CopeX\AutoRedirectHyva\Observer\BrowserLanguageRedirect`
- **Function**: Server-side language detection and redirection

### JavaScript Component
- **Template**: `language-detector.phtml`
- **Function**: Client-side language detection as fallback
- **Integration**: Works with Hyva's Alpine.js framework

### Cookie
- **Name**: `copex_language_detected`
- **Purpose**: Prevent repeated redirections
- **Lifetime**: Configurable (default: 30 days)

## Exclusions

The module will NOT redirect in the following cases:
- AJAX requests
- POST requests
- Admin panel requests (`/admin`)
- API requests (`/rest/`, `/soap/`)
- Users who have already been redirected (cookie exists)
- When module is disabled

## Debugging

Enable debug mode in the configuration to see console logs:
- Browser language detection process
- Language mapping results
- Redirect decisions
- Error messages

## Store Configuration Requirements

Ensure your Magento stores are properly configured:
1. **Multiple Store Views**: Create separate store views for German and English
2. **Locale Configuration**: Set appropriate locales (`de_DE`, `en_US`, etc.)
3. **Base URLs**: Configure different base URLs if using different domains

## Compatibility

- **Magento**: 2.4.x
- **PHP**: 8.1+
- **Theme**: Hyva Theme
- **Dependencies**: Magento_Store, Magento_Customer

## Support

For issues or questions, contact the CopeX development team.

## License

Copyright (c) CopeX (https://copex.io/)
