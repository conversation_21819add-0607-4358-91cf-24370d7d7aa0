<?php
/**
 * CopeX AutoRedirectHyva Browser Language Redirect Observer
 *
 * @category  CopeX
 *
 * @package   CopeX_AutoRedirectHyva
 *
 * <AUTHOR> Team
 *
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\AutoRedirectHyva\Observer;

use Cope<PERSON>\AutoRedirectHyva\Helper\LanguageDetection;
use Magento\Framework\App\ActionFlag;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\App\Response\RedirectInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Store\Model\StoreManagerInterface;

class BrowserLanguageRedirect implements ObserverInterface
{
    private LanguageDetection $languageDetectionHelper;

    private RedirectInterface $redirect;

    private StoreManagerInterface $storeManager;

    private ActionFlag $actionFlag;

    private ResponseInterface $response;

    public function __construct(
        LanguageDetection $languageDetectionHelper,
        RedirectInterface $redirect,
        StoreManagerInterface $storeManager,
        ActionFlag $actionFlag,
        ResponseInterface $response
    ) {
        $this->languageDetectionHelper = $languageDetectionHelper;
        $this->redirect = $redirect;
        $this->storeManager = $storeManager;
        $this->actionFlag = $actionFlag;
        $this->response = $response;
    }

    /**
     * Execute browser language detection and redirect
     */
    public function execute(Observer $observer): void
    {
        try {
            // Check if we should perform redirect
            if (! $this->languageDetectionHelper->shouldRedirect()) {
                return;
            }

            // Get browser language
            $detectedLanguage = $this->languageDetectionHelper->getBrowserLanguage();

            if (! $detectedLanguage) {
                $this->languageDetectionHelper->logDebug('No browser language detected');
                $this->languageDetectionHelper->markLanguageDetected();
                return;
            }

            $this->languageDetectionHelper->logDebug('Detected browser language: ' . $detectedLanguage);

            // Get current store
            $currentStore = $this->storeManager->getStore();
            $currentStoreLocale = $currentStore->getConfig('general/locale/code');
            $currentStoreLanguage = substr($currentStoreLocale, 0, 2);

            // If current store already matches detected language, mark as detected and return
            if ($currentStoreLanguage === $detectedLanguage) {
                $this->languageDetectionHelper->logDebug('Current store already matches detected language');
                $this->languageDetectionHelper->markLanguageDetected();
                return;
            }

            // Find target store for detected language
            $targetStore = $this->languageDetectionHelper->getTargetStoreForLanguage($detectedLanguage);

            if (! $targetStore) {
                $this->languageDetectionHelper->logDebug('No target store found for language: ' . $detectedLanguage);
                $this->languageDetectionHelper->markLanguageDetected();
                return;
            }

            // Perform redirect
            $this->performRedirect($targetStore, $observer);
        } catch (\Exception $e) {
            $this->languageDetectionHelper->logDebug('Error in browser language redirect: ' . $e->getMessage());
            // Mark as detected to prevent infinite loops
            $this->languageDetectionHelper->markLanguageDetected();
        }
    }

    /**
     * Perform the actual redirect to target store
     */
    private function performRedirect(\Magento\Store\Api\Data\StoreInterface $targetStore, Observer $observer): void
    {
        $request = $observer->getEvent()->getRequest();
        $controller = $observer->getEvent()->getControllerAction();

        // Build target URL
        $currentUrl = $request->getRequestUri();
        $targetBaseUrl = $targetStore->getBaseUrl();

        // Remove any existing store code from URL
        $cleanUrl = $this->cleanUrlFromStoreCode($currentUrl);
        $targetUrl = rtrim($targetBaseUrl, '/') . $cleanUrl;

        $this->languageDetectionHelper->logDebug('Redirecting to: ' . $targetUrl);

        // Mark language as detected before redirect
        $this->languageDetectionHelper->markLanguageDetected();

        // Set redirect response
        $this->response->setRedirect($targetUrl, 302);

        // Stop further processing
        $this->actionFlag->set('', ActionInterface::FLAG_NO_DISPATCH, true);
    }

    /**
     * Clean URL from store code
     */
    private function cleanUrlFromStoreCode(string $url): string
    {
        // Remove store codes from URL if present
        $stores = $this->storeManager->getStores();
        foreach ($stores as $store) {
            $storeCode = $store->getCode();
            if (strpos($url, '/' . $storeCode . '/') === 0) {
                return substr($url, strlen('/' . $storeCode));
            }
        }

        return $url;
    }
}
