<?xml version="1.0"?>
<!--
/**
 * CopeX AutoRedirectHyva System Configuration
 *
 * @category  CopeX
 * @package   CopeX_AutoRedirectHyva
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="copex" translate="label" sortOrder="200">
            <label>CopeX</label>
        </tab>
        <section id="copex_autoredirect" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Auto Redirect Hyva</label>
            <tab>copex</tab>
            <resource>CopeX_AutoRedirectHyva::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Auto Redirect</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable automatic redirection based on browser language detection</comment>
                </field>
                <field id="debug" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Debug Mode</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable debug logging for language detection (check browser console)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="cookie_lifetime" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Cookie Lifetime (days)</label>
                    <comment>How long to remember that language detection was performed (default: 30 days)</comment>
                    <validate>validate-number validate-greater-than-zero</validate>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
            <group id="language_mapping" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Language Mapping</label>
                <comment>Configure which browser languages should redirect to which store views</comment>
                <depends>
                    <field id="copex_autoredirect/general/enabled">1</field>
                </depends>
                <field id="german_languages" translate="label comment" type="textarea" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>German Browser Languages</label>
                    <comment>Browser languages that should redirect to German store (one per line, e.g., de, de-DE, de-AT)</comment>
                </field>
                <field id="english_languages" translate="label comment" type="textarea" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>English Browser Languages</label>
                    <comment>Browser languages that should redirect to English store (one per line, e.g., en, en-US, en-GB)</comment>
                </field>
            </group>
        </section>
    </system>
</config>
