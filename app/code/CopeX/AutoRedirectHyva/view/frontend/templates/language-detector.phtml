<?php
/**
 * CopeX AutoRedirectHyva Language Detector Template
 *
 * @category  CopeX
 * @package   CopeX_AutoRedirectHyva
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

declare(strict_types=1);

use CopeX\AutoRedirectHyva\Block\LanguageDetector;
use Magento\Framework\Escaper;

/** @var LanguageDetector $block */
/** @var Escaper $escaper */

// Only render if detection should be performed
if (!$block->shouldPerformDetection()) {
    return;
}
?>

<script>
(function() {
    'use strict';
    
    // Configuration
    const config = {
        cookieName: '<?= $escaper->escapeJs($block->getCookieName()) ?>',
        availableStores: <?= /* @noEscape */ $block->getAvailableStoresJson() ?>,
        currentStore: <?= /* @noEscape */ $block->getCurrentStoreJson() ?>,
        languageMapping: <?= /* @noEscape */ $block->getLanguageMappingJson() ?>,
        debug: <?= $block->isDebugEnabled() ? 'true' : 'false' ?>
    };

    /**
     * Log debug messages
     */
    function debugLog(message, data = null) {
        if (config.debug) {
            console.log('[CopeX AutoRedirect]', message, data || '');
        }
    }

    /**
     * Set cookie
     */
    function setCookie(name, value, days = 30) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    }

    /**
     * Get cookie value
     */
    function getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    /**
     * Get browser languages from navigator
     */
    function getBrowserLanguages() {
        const languages = [];
        
        // Get languages from navigator
        if (navigator.languages && navigator.languages.length) {
            languages.push(...navigator.languages);
        } else if (navigator.language) {
            languages.push(navigator.language);
        } else if (navigator.userLanguage) {
            languages.push(navigator.userLanguage);
        }
        
        return languages.map(lang => lang.toLowerCase());
    }

    /**
     * Map browser language to store language
     */
    function mapBrowserLanguageToStoreLanguage(browserLang) {
        for (const [storeLang, browserLanguages] of Object.entries(config.languageMapping)) {
            for (const supportedLang of browserLanguages) {
                if (browserLang.startsWith(supportedLang.toLowerCase())) {
                    return storeLang;
                }
            }
        }
        return null;
    }

    /**
     * Find target store for detected language
     */
    function findTargetStore(detectedLanguage) {
        return config.availableStores.find(store => store.language === detectedLanguage);
    }

    /**
     * Perform language detection and redirect
     */
    function performLanguageDetection() {
        debugLog('Starting language detection');
        
        // Check if already detected
        if (getCookie(config.cookieName)) {
            debugLog('Language already detected, skipping');
            return;
        }

        // Get browser languages
        const browserLanguages = getBrowserLanguages();
        debugLog('Browser languages:', browserLanguages);

        if (!browserLanguages.length) {
            debugLog('No browser languages detected');
            setCookie(config.cookieName, '1');
            return;
        }

        // Find best matching language
        let detectedLanguage = null;
        for (const browserLang of browserLanguages) {
            detectedLanguage = mapBrowserLanguageToStoreLanguage(browserLang);
            if (detectedLanguage) {
                debugLog('Detected language:', detectedLanguage, 'from browser language:', browserLang);
                break;
            }
        }

        if (!detectedLanguage) {
            debugLog('No matching language found');
            setCookie(config.cookieName, '1');
            return;
        }

        // Check if current store already matches
        if (config.currentStore.language === detectedLanguage) {
            debugLog('Current store already matches detected language');
            setCookie(config.cookieName, '1');
            return;
        }

        // Find target store
        const targetStore = findTargetStore(detectedLanguage);
        if (!targetStore) {
            debugLog('No target store found for language:', detectedLanguage);
            setCookie(config.cookieName, '1');
            return;
        }

        // Perform redirect
        debugLog('Redirecting to store:', targetStore);
        setCookie(config.cookieName, '1');
        
        // Build target URL
        let targetUrl = targetStore.base_url;
        const currentPath = window.location.pathname + window.location.search + window.location.hash;
        
        // Remove store code from current path if present
        let cleanPath = currentPath;
        config.availableStores.forEach(store => {
            const storeCodePath = '/' + store.code + '/';
            if (cleanPath.startsWith(storeCodePath)) {
                cleanPath = cleanPath.substring(storeCodePath.length - 1);
            }
        });
        
        targetUrl = targetUrl.replace(/\/$/, '') + cleanPath;
        
        debugLog('Redirecting to:', targetUrl);
        window.location.href = targetUrl;
    }

    // Execute when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', performLanguageDetection);
    } else {
        performLanguageDetection();
    }
})();
</script>
