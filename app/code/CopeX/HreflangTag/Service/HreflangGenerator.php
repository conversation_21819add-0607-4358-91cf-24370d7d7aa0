<?php
/**
 * CopeX HreflangTag Generator Service
 *
 * @category  CopeX
 *
 * @package   CopeX_HreflangTag
 *
 * <AUTHOR> Team
 *
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\HreflangTag\Service;

use CopeX\HreflangTag\Helper\Data as HreflangHelper;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Visibility;
use Magento\CatalogInventory\Api\StockRegistryInterface;
use Magento\Cms\Api\PageRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;

class HreflangGenerator
{
    private HreflangHelper $hreflangHelper;

    private StoreManagerInterface $storeManager;

    private ProductRepositoryInterface $productRepository;

    private CategoryRepositoryInterface $categoryRepository;

    private PageRepositoryInterface $pageRepository;

    private SearchCriteriaBuilder $searchCriteriaBuilder;

    private StockRegistryInterface $stockRegistry;

    public function __construct(
        HreflangHelper $hreflangHelper,
        StoreManagerInterface $storeManager,
        ProductRepositoryInterface $productRepository,
        CategoryRepositoryInterface $categoryRepository,
        PageRepositoryInterface $pageRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        StockRegistryInterface $stockRegistry
    ) {
        $this->hreflangHelper = $hreflangHelper;
        $this->storeManager = $storeManager;
        $this->productRepository = $productRepository;
        $this->categoryRepository = $categoryRepository;
        $this->pageRepository = $pageRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->stockRegistry = $stockRegistry;
    }

    /**
     * Generate hreflang tags for product
     */
    public function generateProductHreflangTags(int $productId, int $currentStoreId): array
    {
        if (! $this->hreflangHelper->isProductsEnabled($currentStoreId)) {
            return [];
        }

        return $this->generateHreflangTagsForEntity(
            'product',
            $productId,
            $currentStoreId,
            function ($store) use ($productId) {
                return $this->processProductForStore($productId, $store);
            }
        );
    }

    /**
     * Generate hreflang tags for category
     */
    public function generateCategoryHreflangTags(int $categoryId, int $currentStoreId): array
    {
        if (! $this->hreflangHelper->isCategoriesEnabled($currentStoreId)) {
            return [];
        }

        return $this->generateHreflangTagsForEntity(
            'category',
            $categoryId,
            $currentStoreId,
            function ($store) use ($categoryId) {
                return $this->processCategoryForStore($categoryId, $store);
            }
        );
    }

    /**
     * Generate hreflang tags for CMS page
     */
    public function generateCmsPageHreflangTags(string $pageIdentifier, int $currentStoreId): array
    {
        if (! $this->hreflangHelper->isCmsEnabled($currentStoreId)) {
            return [];
        }

        if (! $this->isCmsPageAllowed($pageIdentifier, $currentStoreId)) {
            return [];
        }

        return $this->generateHreflangTagsForEntity(
            'cms',
            $pageIdentifier,
            $currentStoreId,
            function ($store) use ($pageIdentifier) {
                return $this->processCmsPageForStore($pageIdentifier, $store);
            }
        );
    }

    /**
     * Check if product is valid for hreflang generation
     */
    private function isProductValidForHreflang(\Magento\Catalog\Api\Data\ProductInterface $product, int $storeId): bool
    {
        // Check status
        if ($this->hreflangHelper->shouldCheckProductStatus($storeId)) {
            if ($product->getStatus() !== Status::STATUS_ENABLED) {
                return false;
            }
        }

        // Check visibility
        if ($this->hreflangHelper->shouldCheckProductVisibility($storeId)) {
            $visibleInCatalog = [
                Visibility::VISIBILITY_IN_CATALOG,
                Visibility::VISIBILITY_IN_SEARCH,
                Visibility::VISIBILITY_BOTH,
            ];

            if (! in_array($product->getVisibility(), $visibleInCatalog)) {
                return false;
            }
        }

        // Check stock
        if ($this->hreflangHelper->shouldCheckProductStock($storeId)) {
            try {
                $stockItem = $this->stockRegistry->getStockItem($product->getId(), $storeId);
                if (! $stockItem->getIsInStock()) {
                    return false;
                }
            } catch (\Exception $e) {
                $this->hreflangHelper->logDebug("Could not check stock for product {$product->getId()}: " . $e->getMessage());
            }
        }

        return true;
    }

    /**
     * Check if category is valid for hreflang generation
     */
    private function isCategoryValidForHreflang(\Magento\Catalog\Api\Data\CategoryInterface $category, int $storeId): bool
    {
        // Check if root category should be excluded
        if ($this->hreflangHelper->shouldExcludeRootCategory($storeId)) {
            if ($category->getLevel() <= 1) {
                return false;
            }
        }

        // Check active status
        if ($this->hreflangHelper->shouldCheckCategoryActive($storeId)) {
            if (! $category->getIsActive()) {
                return false;
            }
        }

        // Check include in menu
        if ($this->hreflangHelper->shouldCheckCategoryMenu($storeId)) {
            if (! $category->getIncludeInMenu()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if CMS page is valid for hreflang generation
     */
    private function isCmsPageValidForHreflang(\Magento\Cms\Api\Data\PageInterface $page, int $storeId): bool
    {
        // Check active status
        if ($this->hreflangHelper->shouldCheckCmsActive($storeId)) {
            if (! $page->isActive()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Common method to generate hreflang tags for any entity type
     */
    private function generateHreflangTagsForEntity(string $entityType, $entityId, int $currentStoreId, callable $processor): array
    {
        $hreflangTags = [];
        $availableStores = $this->hreflangHelper->getAvailableStores();

        foreach ($availableStores as $store) {
            $result = $processor($store);
            if ($result) {
                $hreflangTags[] = $result;
            }
        }

        return $this->addXDefaultTag($hreflangTags, $currentStoreId);
    }

    /**
     * Process product for specific store
     */
    private function processProductForStore(int $productId, $store): ?array
    {
        try {
            $product = $this->productRepository->getById($productId, false, $store->getId());

            if (! $this->isProductValidForHreflang($product, $store->getId())) {
                return null;
            }

            return $this->createHreflangTag(
                $this->getProductUrl($product, $store),
                $this->getStoreHreflangCode($store)
            );
        } catch (NoSuchEntityException $e) {
            $this->hreflangHelper->logDebug("Product {$productId} not found in store {$store->getId()}");
            return null;
        } catch (\Exception $e) {
            $this->hreflangHelper->logError("Error processing product {$productId} for store {$store->getId()}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Process category for specific store
     */
    private function processCategoryForStore(int $categoryId, $store): ?array
    {
        try {
            $category = $this->categoryRepository->get($categoryId, $store->getId());

            if (! $this->isCategoryValidForHreflang($category, $store->getId())) {
                return null;
            }

            return $this->createHreflangTag(
                $this->getCategoryUrl($category, $store),
                $this->getStoreHreflangCode($store)
            );
        } catch (NoSuchEntityException $e) {
            $this->hreflangHelper->logDebug("Category {$categoryId} not found in store {$store->getId()}");
            return null;
        } catch (\Exception $e) {
            $this->hreflangHelper->logError("Error processing category {$categoryId} for store {$store->getId()}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Process CMS page for specific store
     */
    private function processCmsPageForStore(string $pageIdentifier, $store): ?array
    {
        try {
            $page = $this->getCmsPageByIdentifier($pageIdentifier, $store->getId());

            if (! $page || ! $this->isCmsPageValidForHreflang($page, $store->getId())) {
                return null;
            }

            return $this->createHreflangTag(
                $this->getCmsPageUrl($page, $store),
                $this->getStoreHreflangCode($store)
            );
        } catch (\Exception $e) {
            $this->hreflangHelper->logError("Error processing CMS page {$pageIdentifier} for store {$store->getId()}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if CMS page is allowed for hreflang generation
     */
    private function isCmsPageAllowed(string $pageIdentifier, int $currentStoreId): bool
    {
        // Check if page is excluded
        $excludedPages = $this->hreflangHelper->getExcludedCmsPages($currentStoreId);
        if (in_array($pageIdentifier, $excludedPages)) {
            return false;
        }

        // Special handling for homepage
        if ($pageIdentifier === 'home' && ! $this->hreflangHelper->shouldIncludeHomepage($currentStoreId)) {
            return false;
        }

        return true;
    }

    /**
     * Create hreflang tag array
     */
    private function createHreflangTag(?string $url, ?string $hreflangCode): ?array
    {
        if (! $url || ! $hreflangCode) {
            return null;
        }

        return [
            'hreflang' => $hreflangCode,
            'href' => $url,
        ];
    }

    /**
     * Add x-default tag if configured
     */
    private function addXDefaultTag(array $hreflangTags, int $currentStoreId): array
    {
        if (! $this->hreflangHelper->shouldAddXDefault($currentStoreId)) {
            return $hreflangTags;
        }

        $defaultTag = $this->getDefaultHreflangTag($hreflangTags, $currentStoreId);
        if ($defaultTag) {
            $hreflangTags[] = $defaultTag;
        }

        return $hreflangTags;
    }

    /**
     * Get product URL for store
     */
    private function getProductUrl(\Magento\Catalog\Api\Data\ProductInterface $product, \Magento\Store\Api\Data\StoreInterface $store): ?string
    {
        try {
            $originalStore = $this->storeManager->getStore();
            $this->storeManager->setCurrentStore($store->getId());
            $url = $product->getProductUrl();
            $this->storeManager->setCurrentStore($originalStore);
            return $url;
        } catch (\Exception $e) {
            $this->hreflangHelper->logError('Error getting product URL: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get category URL for store
     */
    private function getCategoryUrl(\Magento\Catalog\Api\Data\CategoryInterface $category, \Magento\Store\Api\Data\StoreInterface $store): ?string
    {
        try {
            $originalStore = $this->storeManager->getStore();
            $this->storeManager->setCurrentStore($store->getId());
            $url = $category->getUrl();
            $this->storeManager->setCurrentStore($originalStore);
            return $url;
        } catch (\Exception $e) {
            $this->hreflangHelper->logError('Error getting category URL: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get CMS page URL for store
     */
    private function getCmsPageUrl(\Magento\Cms\Api\Data\PageInterface $page, \Magento\Store\Api\Data\StoreInterface $store): ?string
    {
        try {
            $originalStore = $this->storeManager->getStore();
            $this->storeManager->setCurrentStore($store->getId());
            $baseUrl = $store->getUrl();
            $url = rtrim($baseUrl, '/') . '/' . ltrim($page->getIdentifier(), '/');
            $this->storeManager->setCurrentStore($originalStore);
            return $url;
        } catch (\Exception $e) {
            $this->hreflangHelper->logError('Error getting CMS page URL: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get CMS page by identifier and store
     */
    private function getCmsPageByIdentifier(string $identifier, int $storeId): ?\Magento\Cms\Api\Data\PageInterface
    {
        try {
            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter('page_id', $identifier)
                ->addFilter('store_id', [$storeId, 0], 'in')
                ->create();

            $pages = $this->pageRepository->getList($searchCriteria)->getItems();

            return ! empty($pages) ? reset($pages) : null;
        } catch (\Exception $e) {
            $this->hreflangHelper->logError('Error getting CMS page: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get hreflang code for store
     */
    private function getStoreHreflangCode(\Magento\Store\Api\Data\StoreInterface $store): ?string
    {
        $locale = $store->getConfig('general/locale/code');
        return $this->hreflangHelper->getHreflangCode($locale, $store->getId());
    }

    /**
     * Get default hreflang tag (x-default)
     *
     * @return array|null
     */
    private function getDefaultHreflangTag(array $hreflangTags, int $currentStoreId): ?array
    {
        $defaultLanguage = $this->hreflangHelper->getDefaultLanguage($currentStoreId);

        foreach ($hreflangTags as $tag) {
            if ($tag['hreflang'] === $defaultLanguage) {
                return [
                    'hreflang' => 'x-default',
                    'href' => $tag['href'],
                ];
            }
        }

        return null;
    }
}
