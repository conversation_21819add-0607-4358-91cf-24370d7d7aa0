<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="customer">
            <group id="deleteme" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Delete Me Configuration</label>
                <field id="header_text" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Title</label>
                    <comment>Title of the 'delete my account' section in the customer account.</comment>
                </field>
                <field id="delete_text" translate="label" type="textarea" sortOrder="1" showInDefault="1"
                       showInWebsite="1"
                       showInStore="1">
                    <label>Content</label>
                    <comment>Content of the 'delete my account' section in the customer account.</comment>
                </field>
                <field id="button_text" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Button Text</label>
                    <comment>This text will appear in the button.</comment>
                </field>
                <field id="button_css_class" translate="label" type="text" sortOrder="1" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Button css class</label>
                    <comment>Choose a custom styling for your button.</comment>
                </field>
            </group>
        </section>
    </system>
</config>
