<?php

namespace CopeX\DeleteMe\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    public const XML_PATH_DELETEME = 'customer/deleteme/';
    public const XML_PATH_DELETEME_TITLE = 'customer/deleteme/header_text';
    public const XML_PATH_DELETEME_CONTENT = 'customer/deleteme/delete_text';
    public const XML_PATH_DELETEME_BTN_TEXT = 'customer/deleteme/button_text';
    public const XML_PATH_DELETEME_BTN_CSS = 'customer/deleteme/button_css_class';

    protected $_storeManager;
    protected $_scopeConfig;

    public function __construct(
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        Context $context
    ) {
        parent::__construct($context);
        $this->_storeManager = $storeManager;
        $this->_scopeConfig = $context->getScopeConfig();
    }

    public function getGeneralConfig($code)
    {
        return $this->getConfigValue(self::XML_PATH_DELETEME . $code);
    }

    public function getTitleText()
    {
        return $this->getConfigValue(self::XML_PATH_DELETEME_TITLE);
    }

    public function getContentText()
    {
        return $this->getConfigValue(self::XML_PATH_DELETEME_CONTENT);
    }

    public function getBtnText()
    {
        return $this->getConfigValue(self::XML_PATH_DELETEME_BTN_TEXT);
    }

    public function getBtnCssClass()
    {
        return $this->getConfigValue(self::XML_PATH_DELETEME_BTN_CSS);
    }

    private function getConfigValue($field)
    {
        return $this->_scopeConfig->getValue($field, ScopeInterface::SCOPE_STORE);
    }
}
