<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace CopeX\DeleteMe\Controller\Account;

use Magento\Customer\Model\Registration;
use Magento\Customer\Model\ResourceModel\CustomerRepository;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;

class Send extends \Magento\Customer\Controller\AbstractAccount
{
    protected \Magento\Customer\Model\Registration $registration;

    protected Session $session;

    protected CustomerRepository $customerRepository;

    protected PageFactory $resultPageFactory;

    protected \Magento\Framework\Registry $registry;

    public function __construct(
        Context $context,
        Session $customerSession,
        PageFactory $resultPageFactory,
        Registration $registration,
        CustomerRepository $customerRepository,
        \Magento\Framework\Registry $registry
    ) {
        $this->session = $customerSession;
        $this->resultPageFactory = $resultPageFactory;
        $this->registration = $registration;
        $this->customerRepository = $customerRepository;
        $this->registry = $registry;
        parent::__construct($context);
    }

    public function execute()
    {
        if ($this->session->isLoggedIn() || ! $this->registration->isAllowed()) {
            $customer = $this->customerRepository->getById($this->session->getCustomerId());
            try {
                $this->registry->register('isSecureArea', true);
                $this->customerRepository->delete($customer);

                /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
                $resultRedirect = $this->resultRedirectFactory->create();
                $resultRedirect->setPath('/customer/account/create');
                return $resultRedirect;
            } catch (\Exception $e) {
            }
        }
    }
}
