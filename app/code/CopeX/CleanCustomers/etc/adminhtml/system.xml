<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="customer">
            <group id="clean_customers" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                   translate="label">
                <label>Clean Up Customer Data</label>
                <field id="clean_customers_no_orders" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10"
                       translate="label" type="select">
                    <label>Customers (no orders)</label>
                    <source_model>CopeX\CleanCustomers\Model\Config\Source\Schema</source_model>
                    <comment>Customers without orders and older than X units will be cleaned up.</comment>
                </field>
                <field id="clean_after_amount_customer_no_orders" showInDefault="1" showInStore="1" showInWebsite="1"
                       sortOrder="20"
                       translate="label" type="text">
                    <label>Clean up after ... (customers no orders)</label>
                    <depends>
                        <field id="clean_customers_no_orders" separator=",">1,2</field>
                    </depends>
                </field>
                <field id="clean_after_unit_customer_no_orders" showInDefault="1" showInStore="1" showInWebsite="1"
                       sortOrder="30"
                       translate="label" type="select">
                    <label>Unit (customers no orders)</label>
                    <source_model>CopeX\CleanCustomers\Model\Config\Source\Unit</source_model>
                    <depends>
                        <field id="clean_customers_no_orders" separator=",">1,2</field>
                    </depends>
                </field>
                <field id="clean_customers_inactive" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="40"
                       translate="label" type="select">
                    <label>Customers (inactive)</label>
                    <source_model>CopeX\CleanCustomers\Model\Config\Source\Schema</source_model>
                    <comment>Customers inactive since X units AND their orders will be cleaned up.</comment>
                </field>
                <field id="clean_after_amount_customer_inactive" showInDefault="1" showInStore="1" showInWebsite="1"
                       sortOrder="50"
                       translate="label" type="text">
                    <label>Clean up after ... (customers inactive)</label>
                    <depends>
                        <field id="clean_customers_inactive" separator=",">1,2</field>
                    </depends>
                </field>
                <field id="clean_after_unit_customer_inactive" showInDefault="1" showInStore="1" showInWebsite="1"
                       sortOrder="60"
                       translate="label" type="select">
                    <label>Unit (customers inactive)</label>
                    <source_model>CopeX\CleanCustomers\Model\Config\Source\Unit</source_model>
                    <depends>
                        <field id="clean_customers_inactive" separator=",">1,2</field>
                    </depends>
                </field>
                <field id="clean_orders" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="70"
                       translate="label" type="select">
                    <label>Guest Orders</label>
                    <source_model>CopeX\CleanCustomers\Model\Config\Source\Schema</source_model>
                    <comment>Guest orders older than X units will be cleaned up.</comment>
                </field>
                <field id="clean_after_amount_order" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="80"
                       translate="label" type="text">
                    <label>Clean up after ... (guest orders)</label>
                    <depends>
                        <field id="clean_orders" separator=",">1,2</field>
                    </depends>
                </field>
                <field id="clean_after_unit_order" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90"
                       translate="label" type="select">
                    <label>Unit (guest orders)</label>
                    <source_model>CopeX\CleanCustomers\Model\Config\Source\Unit</source_model>
                    <depends>
                        <field id="clean_orders" separator=",">1,2</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
