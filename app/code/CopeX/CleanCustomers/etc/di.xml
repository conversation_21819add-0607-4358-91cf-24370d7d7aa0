<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="copex_cleancustomers" xsi:type="object">CopeX\CleanCustomers\Console\Command\CleanCustomers</item>
            </argument>
        </arguments>
    </type>
    <type name="CopeX\CleanCustomers\Model\ProcessorList">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="customer" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Customer</item>
                <item name="order" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Order</item>
                <item name="quote" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Quote</item>
            </argument>
        </arguments>
    </type>
    <type name="CopeX\CleanCustomers\Model\Privacy\Customer">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="address" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Customer\Address</item>
                <item name="compare" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Customer\Compare</item>
                <item name="quote" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Customer\Quote</item>
                <item name="reviews" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Customer\Reviews</item>
                <item name="wishlist" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Customer\Wishlist</item>
                <item name="data" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Customer\Data</item>
            </argument>
            <argument name="orderProcessors" xsi:type="array">
                <item name="address" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Order\Address</item>
                <item name="data" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Order\Data</item>
            </argument>
        </arguments>
    </type>
    <type name="CopeX\CleanCustomers\Model\Privacy\Order">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="address" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Order\Address</item>
                <item name="data" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Order\Data</item>
            </argument>
        </arguments>
    </type>
    <type name="CopeX\CleanCustomers\Model\Privacy\Quote">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="address" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Quote\Address</item>
                <item name="data" xsi:type="object">CopeX\CleanCustomers\Model\Privacy\Quote\Data</item>
            </argument>
        </arguments>
    </type>
</config>
