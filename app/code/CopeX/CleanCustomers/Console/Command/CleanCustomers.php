<?php

namespace CopeX\CleanCustomers\Console\Command;

use CopeX\CleanCustomers\Model\ProcessorList;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Registry;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class AbstractCommand
 *
 * @package CopeX\Import\Console\Command
 */
class CleanCustomers extends Command
{
    private ProcessorList $processorList;

    private State $state;

    private Registry $registry;

    /**
     * AbstractCommand constructor.
     */
    public function __construct(
        ProcessorList $processorList,
        State $state,
        Registry $registry
    ) {
        $this->processorList = $processorList;
        $this->state = $state;
        $this->registry = $registry;
        parent::__construct('copex:cleancustomers');
    }

    protected function configure(): void
    {
        parent::configure();
        $this->setName('copex:cleancustomers');
        $this->setDescription('Clean customers data');
        $this->addArgument('type', InputArgument::REQUIRED, 'Pleas enter a type.');
        $this->addOption('dryrun', 'd', InputOption::VALUE_OPTIONAL, 'Display items to process', '0');
    }

    /**
     * Execute the console command to process a special type of clean customer processor
     * Available types are: customer, order, quote, all
     */
    protected function execute(InputInterface $input, OutputInterface $output): void
    {
        try {
            $this->state->setAreaCode(Area::AREA_ADMINHTML);
            $this->registry->register('isSecureArea', true);
            $time = microtime(true);
            $type = $input->getArgument('type');

            if (! $this->processorList->hasProcessor($type)) {
                $output->writeln("Could not find type {$type}.");
                $output->writeln('Available types are: ' . implode(', ', array_keys($this->processorList->getProcessors())) . ', all');
            } else {
                $processors = $this->processorList->getProcessors($type);

                foreach ($processors as $processor) {
                    if ($input->getOption('dryrun')) {
                        $return = $processor->dryRun();
                        $output->writeln(implode("\n", $return));
                    } else {
                        $processor->execute();
                    }
                }
            }
        } catch (\Exception $e) {
            $output->writeln('EXCEPTION: ' . $e->getMessage());
            exit(1);
        }

        $output->writeln('Process finished. Elapsed time: ' . round(microtime(true) - $time, 2) . 's' . "\n");
    }
}
