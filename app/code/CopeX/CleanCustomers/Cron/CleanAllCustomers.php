<?php

namespace CopeX\CleanCustomers\Cron;

use Magento\Framework\Registry;

class CleanAllCustomers
{
    protected $logger;
    protected $helper;
    protected $customerCollection;
    protected $orderCollection;
    private ProcessorList $processorList;

    private Registry $registry;

    public function __construct(
        \Psr\Log\LoggerInterface $logger,
        \CopeX\CleanCustomers\Model\ProcessorList $processorList,
        Registry $registry
    ) {
        $this->logger = $logger;
        $this->processorList = $processorList;
        $this->registry = $registry;
    }

    /**
     * Execute the cron to process all clean customer processors listed in CopeX/CleanCustomers/etc/di.xml
     */
    public function execute(): void
    {
        $this->registry->register('isSecureArea', true);
        $processors = $this->processorList->getProcessors();

        foreach ($processors as $processor) {
            $processor->execute();
        }
    }
}
