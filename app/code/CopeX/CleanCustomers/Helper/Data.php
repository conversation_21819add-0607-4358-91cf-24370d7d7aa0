<?php

namespace CopeX\CleanCustomers\Helper;

use DateInterval;
use DateTime;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    public const XML_CLEAN_CUSTOMER = 'customer/clean_customers/';
    public const ANONYMOUS_STR = '###########';
    public const ANONYMOUS_EMAIL = '@anonymous.com';

    public const XML_CUSTOMER_NO_ORDERS = 'clean_customers_no_orders';
    public const XML_CUSTOMER_NO_ORDERS_AMOUNT = 'clean_after_amount_customer_no_orders';
    public const XML_CUSTOMER_NO_ORDERS_UNIT = 'clean_after_unit_customer_no_orders';

    public const XML_CUSTOMER_INACTIVE = 'clean_customers_inactive';
    public const XML_CUSTOMER_INACTIVE_AMOUNT = 'clean_after_amount_customer_inactive';
    public const XML_CUSTOMER_INACTIVE_UNIT = 'clean_after_unit_customer_inactive';

    public const XML_ORDERS = 'clean_orders';
    public const XML_ORDERS_AMOUNT = 'clean_after_amount_order';
    public const XML_ORDERS_UNIT = 'clean_after_unit_order';

    public const DEFAULT_CLEAN_CUSTOMER_NO_ORDERS = 1;
    public const DEFAULT_CLEAN_CUSTOMER_INACTIVE = 7;
    public const DEFAULT_CLEAN_ORDER = 7;
    public const DEFAULT_UNIT = 'Y';

    /**
     * Get an anonymous email address to anonymize customer data and address email address
     */
    public function getAnonymousEmail(string $id): string
    {
        return uniqid($id) . self::ANONYMOUS_EMAIL;
    }

    /**
     * Get configuration value or default value if configuration value is not set
     *
     * @param $default
     */
    public function getGeneralConfig(string $code, $default, null $storeId = null): mixed
    {
        return $this->getConfigValue(self::XML_CLEAN_CUSTOMER . $code, $storeId) ?: $default;
    }

    /**
     * Customer (without orders) created before this date will be cleaned up.
     */
    public function getCustomerNoOrdersCleanDate(): string
    {
        $amount = $this->getGeneralConfig(self::XML_CUSTOMER_NO_ORDERS_AMOUNT, self::DEFAULT_CLEAN_CUSTOMER_NO_ORDERS);
        $unit = $this->getGeneralConfig(self::XML_CUSTOMER_NO_ORDERS_UNIT, self::DEFAULT_UNIT);
        return $this->getDateFormatted($amount, $unit);
    }

    /**
     * Customers who were last logged in before this date will be cleaned up.
     */
    public function getCustomerInactiveCleanDate(): string
    {
        $amount = $this->getGeneralConfig(self::XML_CUSTOMER_INACTIVE_AMOUNT, self::DEFAULT_CLEAN_CUSTOMER_INACTIVE);
        $unit = $this->getGeneralConfig(self::XML_CUSTOMER_INACTIVE_UNIT, self::DEFAULT_UNIT);
        return $this->getDateFormatted($amount, $unit);
    }

    /**
     * Orders (and quotes) created before this date will be cleaned up.
     */
    public function getOrderCleanDate(): string
    {
        $amount = $this->getGeneralConfig(self::XML_ORDERS_AMOUNT, self::DEFAULT_CLEAN_ORDER);
        $unit = $this->getGeneralConfig(self::XML_ORDERS_UNIT, self::DEFAULT_UNIT);
        return $this->getDateFormatted($amount, $unit);
    }

    /**
     * Get a configuration value by path (field) and scope.
     */
    protected function getConfigValue(string $field, null $storeId = null): mixed
    {
        return $this->scopeConfig->getValue($field, ScopeInterface::SCOPE_STORE, $storeId);
    }

    /**
     * Returns a formatted date in the past depending on the specified time span (amount) and time unit.
     *
     * @param $amount
     * @param $unit
     */
    protected function getDateFormatted($amount, $unit, string $format = 'Y-m-d'): string
    {
        $date = new DateTime();

        try {
            $date->sub(new DateInterval('P' . $amount . $unit));
        } catch (\Exception $e) {
            $this->_logger->notice('Could not create date interval.');
        }

        return $date->format($format);
    }
}
