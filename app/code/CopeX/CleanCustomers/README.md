# CopeX CleanCustomers Magento 2 Module

This module adds the possibility to automatically clean up customer data (and orders). Dependent on the backend
configuration, customers and orders will be deleted or anonymized after a specified time period.

There are different configuration possibilities for
* customers without orders
* inactive customers (and their orders)
* guest orders

For each of these three types the cleanup mode (nothing/delete/anonymize) could be selected.
The time period after which the cleanup should start could also be specified.

**Customers without orders** and older than the specified time period will be deleted or anonymized. 
The `created_at` date will be checked for this operation.

**Customers inactive** for the specified time period and also their orders will be deleted or anonymized.
The `last_login_at` date of `customer_log` table will be checked for this operation.

**Guest orders** older than the specified time period will be deleted or anonymized. 
The `created_at` date will be checked for this operation.

### Execution
The cleanup will be executed automatically via **cronjob** every night at 1am.
It could also be executed manually via **console command**:
`copex:cleancustomers [-d|--dryrun [DRYRUN]] [--] <type>`

Available types are: customer, order, quote, all

Command examples:
* `copex:cleancustomers all`
* `copex:cleancustomers all --dryrun=1`
