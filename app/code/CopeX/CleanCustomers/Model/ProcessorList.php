<?php

namespace CopeX\CleanCustomers\Model;

use CopeX\CleanCustomers\Model\Privacy\ModelInterface;

class ProcessorList
{
    /** @var array<string> */
    private array $processors;

    /**
     * Constructor
     */
    public function __construct(array $processors = [])
    {
        $this->processors = $processors;
    }

    /**
     * Gets cleanup processor by given type or all available processors.
     *
     * @return array<ModelInterface>
     */
    public function getProcessors(string $type = 'all'): array
    {
        if ($type !== 'all' && $this->hasProcessor($type)) {
            return [$this->processors[$type]];
        }

        if ($type === 'all') {
            return $this->processors;
        }

        return [];
    }

    /**
     * Checks if a processor with the given type exists.
     */
    public function hasProcessor(string $type): bool
    {
        return isset($this->processors[$type]) || $type === 'all';
    }
}
