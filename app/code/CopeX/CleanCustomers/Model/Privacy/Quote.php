<?php

namespace CopeX\CleanCustomers\Model\Privacy;

use CopeX\CleanCustomers\Helper\Data;
use Magento\Quote\Model\ResourceModel\Quote\CollectionFactory;

class Quote extends AbstractModel
{
    protected CollectionFactory $quoteCollectionFactory;

    public function __construct(
        Data $helper,
        CollectionFactory $quoteCollectionFactory,
        array $processors = []
    ) {
        parent::__construct($helper, $processors);
        $this->quoteCollectionFactory = $quoteCollectionFactory;
    }

    /**
     * Collects all quotes which have to be cleaned up and processes clean up dependent on the configured cleanup mode (behavior).
     */
    public function execute(): void
    {
        $behavior = $this->helper->getGeneralConfig(Data::XML_ORDERS, false);

        if ($behavior) {
            $formattedDateOrder = $this->helper->getOrderCleanDate();
            $quotes = $this->quoteCollectionFactory->create();
            $quotes->addFieldToFilter(
                ['customer_firstname', 'customer_firstname'],
                [['neq' => Data::ANONYMOUS_STR], ['null' => true]]
            )
                ->addFieldToFilter('created_at', ['lt' => $formattedDateOrder]);

            foreach ($quotes as $quote) {
                $this->processItem($quote, $behavior);
            }
        }
    }
}
