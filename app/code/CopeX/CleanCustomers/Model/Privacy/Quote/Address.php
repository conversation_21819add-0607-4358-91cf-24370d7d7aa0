<?php

namespace CopeX\CleanCustomers\Model\Privacy\Quote;

use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;

class Address implements PrivacyInterface
{
    /**
     * Executed upon quote address data deletion.
     *
     * @param $quote Quote
     */
    public function delete($quote): void
    {
        $this->doProcess($quote);
    }

    /**
     * Executed upon quote address data anonymization.
     *
     * @param $quote Quote
     */
    public function anonymize($quote): void
    {
        $this->doProcess($quote);
    }

    /**
     * @param $quote Quote
     */
    protected function doProcess($quote): void
    {
        $quote->getAddressesCollection()->walk('delete');
    }
}
