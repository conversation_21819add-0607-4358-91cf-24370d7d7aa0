<?php

namespace CopeX\CleanCustomers\Model\Privacy\Quote;

use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;
use Magento\Quote\Model\ResourceModel\Quote;
use Psr\Log\LoggerInterface;

class Data implements PrivacyInterface
{
    private Quote $quoteResourceModel;

    private LoggerInterface $logger;

    /**
     * Quote constructor.
     */
    public function __construct(
        Quote $quoteResourceModel,
        LoggerInterface $logger
    ) {
        $this->quoteResourceModel = $quoteResourceModel;
        $this->logger = $logger;
    }

    /**
     * Executed upon quote data deletion.
     *
     * @param $quote QuoteModel
     */
    public function delete($quote): void
    {
        $this->doProcess($quote);
    }

    /**
     * Executed upon quote data anonymization.
     *
     * @param $quote QuoteModel
     */
    public function anonymize($quote): void
    {
        $this->doProcess($quote);
    }

    /**
     * Executed upon quote data anonymization.
     *
     * @param $quote QuoteModel
     */
    protected function doProcess($quote): void
    {
        try {
            $this->quoteResourceModel->delete($quote);
        } catch (\Exception $e) {
            $this->logger->notice($e->getMessage());
        }
    }
}
