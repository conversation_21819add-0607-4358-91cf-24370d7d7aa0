<?php

namespace CopeX\CleanCustomers\Model\Privacy;

use CopeX\CleanCustomers\Helper\Data;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory as CustomerCollectionFactory;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;

class Customer extends AbstractModel
{
    private CustomerCollectionFactory $customerCollectionFactory;

    private OrderCollectionFactory $orderCollectionFactory;

    private array $orderProcessors;

    public function __construct(
        Data $helper,
        CustomerCollectionFactory $customerCollectionFactory,
        OrderCollectionFactory $orderCollectionFactory,
        array $processors = [],
        array $orderProcessors = []
    ) {
        parent::__construct($helper, $processors);
        $this->orderProcessors = $orderProcessors;
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->orderCollectionFactory = $orderCollectionFactory;
    }

    /**
     * Executes customer cleanup and triggers customer without orders and inactive customer cleanup
     */
    public function execute(): void
    {
        $this->cleanCustomersWithoutOrders();
        $this->cleanCustomersInactive();
    }

    /**
     * Collects all customers (without orders) which have to be cleaned up and processes clean up dependent on the configured cleanup mode (behavior).
     */
    protected function cleanCustomersWithoutOrders(): void
    {
        $behavior = $this->helper->getGeneralConfig(Data::XML_CUSTOMER_NO_ORDERS, false);

        try {
            if ($behavior) {
                $formattedCleanDate = $this->helper->getCustomerNoOrdersCleanDate();

                $customers = $this->customerCollectionFactory->create()
                    ->addAttributeToFilter('created_at', ['lt' => $formattedCleanDate])
                    ->addAttributeToFilter('firstname', ['neq' => Data::ANONYMOUS_STR]);
                $customers->getSelect()
                    ->joinLeft(
                        ['o' => $this->orderCollectionFactory->create()->getSelect()],
                        'o.customer_id = e.entity_id',
                        ['last_order_date' => 'MAX(o.created_at)']
                    )
                    ->group('entity_id')->assemble();

                foreach ($customers as $customer) {
                    if (! $customer->getLastOrderDate()) {
                        $this->processItem($customer, $behavior);
                    }
                }
            }
        } catch (\Exception $e) {
            echo $e;
        }
    }

    /**
     * Collects all inactive customers which have to be cleaned up and processes clean up dependent on the configured cleanup mode (behavior).
     */
    protected function cleanCustomersInactive(): void
    {
        $behavior = $this->helper->getGeneralConfig(Data::XML_CUSTOMER_INACTIVE, false);

        try {
            if ($behavior) {
                $formattedCleanDate = $this->helper->getCustomerInactiveCleanDate();

                $customers = $this->customerCollectionFactory->create()
                    ->addAttributeToFilter('firstname', ['neq' => Data::ANONYMOUS_STR]);
                $customers->getSelect()
                    ->joinLeft(
                        ['l' => $customers->getTable('customer_log')],
                        'l.customer_id = e.entity_id',
                        ['last_login_date' => 'l.last_login_at']
                    )
                    ->group('entity_id')->assemble();

                foreach ($customers as $customer) {
                    if ($customer->getLastLoginDate() && (strtotime($customer->getLastLoginDate()) < strtotime($formattedCleanDate))) {
                        $orders = $this->orderCollectionFactory->create();
                        $orders->addFieldToFilter('customer_id', ['eq' => $customer->getId()])
                            ->addFieldToFilter('customer_firstname', ['neq' => Data::ANONYMOUS_STR]);

                        foreach ($orders as $order) {
                            $this->processItem($order, $behavior, $this->orderProcessors);
                        }

                        $this->processItem($customer, $behavior);
                    }
                }
            }
        } catch (\Exception $e) {
            echo $e;
        }
    }
}
