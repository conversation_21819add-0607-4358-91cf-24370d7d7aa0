<?php

namespace CopeX\CleanCustomers\Model\Privacy\Customer;

use CopeX\CleanCustomers\Helper\Data as HelperData;
use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;
use Magento\Customer\Model\ResourceModel\Customer;
use Psr\Log\LoggerInterface;

class Data implements PrivacyInterface
{
    private Customer $customerResourceModel;

    private HelperData $helper;

    private LoggerInterface $logger;

    public function __construct(
        Customer $customerResourceModel,
        HelperData $helper,
        LoggerInterface $logger
    ) {
        $this->customerResourceModel = $customerResourceModel;
        $this->helper = $helper;
        $this->logger = $logger;
    }

    /**
     * Executed upon customer data deletion.
     *
     * @param $customer
     */
    public function delete($customer): void
    {
        try {
            $this->customerResourceModel->delete($customer);
        } catch (\Exception $e) {
            $this->logger->notice($e->getMessage());
        }
    }

    /**
     * Executed upon customer data anonymization.
     *
     * @param $customer
     */
    public function anonymize($customer): void
    {
        $customer
            ->setPrefix(HelperData::ANONYMOUS_STR)
            ->setFirstname(HelperData::ANONYMOUS_STR)
            ->setMiddlename(HelperData::ANONYMOUS_STR)
            ->setLastname(HelperData::ANONYMOUS_STR)
            ->setSuffix(HelperData::ANONYMOUS_STR)
            ->setEmail($this->helper->getAnonymousEmail($customer->getId()))
            ->setDob('1970-01-01')
            ->setTaxvat(HelperData::ANONYMOUS_STR)
            ->setGender(0);

        try {
            $this->customerResourceModel->save($customer);
        } catch (\Exception $e) {
            $this->logger->notice($e->getMessage());
        }
    }
}
