<?php

namespace CopeX\CleanCustomers\Model\Privacy\Customer;

use CopeX\CleanCustomers\Helper\Data;
use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;
use Magento\Review\Model\ResourceModel\Review\CollectionFactory;

class Reviews implements PrivacyInterface
{
    private CollectionFactory $collectionFactory;

    public function __construct(
        CollectionFactory $collectionFactory
    ) {
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * Executed upon customer review data deletion.
     *
     * @param $customer
     */
    public function delete($customer): void
    {
        $this->processReviews($customer->getId());
    }

    /**
     * Executed upon customer review data anonymization.
     *
     * @param $customer
     */
    public function anonymize($customer): void
    {
        $this->processReviews($customer->getId());
    }

    /**
     * Process review anonymization.
     *
     * @param $customerId
     */
    protected function processReviews($customerId): void
    {
        $reviews = $this->collectionFactory->create()
            ->addFieldToFilter('customer_id', $customerId);

        foreach ($reviews as $review) {
            $review->setData('nickname', Data::ANONYMOUS_STR);
        }

        $reviews->walk('save');
    }
}
