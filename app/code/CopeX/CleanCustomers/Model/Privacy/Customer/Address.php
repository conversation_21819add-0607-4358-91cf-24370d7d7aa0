<?php

namespace CopeX\CleanCustomers\Model\Privacy\Customer;

use CopeX\CleanCustomers\Helper\Data;
use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;
use Magento\Customer\Model\ResourceModel\Address as AddressResourceModel;
use Psr\Log\LoggerInterface;

class Address implements PrivacyInterface
{
    private AddressResourceModel $addressResourceModel;

    private LoggerInterface $logger;

    /**
     * CustomerAddress constructor.
     */
    public function __construct(
        AddressResourceModel $addressResourceModel,
        LoggerInterface $logger
    ) {
        $this->addressResourceModel = $addressResourceModel;
        $this->logger = $logger;
    }

    /**
     * Executed upon customer address data deletion.
     *
     * @param $customer
     */
    public function delete($customer): void
    {
        foreach ($customer->getAddresses() as $address) {
            try {
                $this->addressResourceModel->delete($address);
            } catch (\Exception $e) {
                $this->logger->notice($e->getMessage());
            }
        }
    }

    /**
     * Executed upon customer address data anonymization.
     *
     * @param $customer
     */
    public function anonymize($customer): void
    {
        foreach ($customer->getAddresses() as $address) {
            $address
                ->setCity(Data::ANONYMOUS_STR)
                ->setCompany(Data::ANONYMOUS_STR)
                ->setFax(Data::ANONYMOUS_STR)
                ->setPrefix(Data::ANONYMOUS_STR)
                ->setFirstname(Data::ANONYMOUS_STR)
                ->setLastname(Data::ANONYMOUS_STR)
                ->setMiddlename(Data::ANONYMOUS_STR)
                ->setSuffix(Data::ANONYMOUS_STR)
                ->setPostcode(Data::ANONYMOUS_STR)
                ->setTelephone(Data::ANONYMOUS_STR);
            $streets = [];

            foreach ($address->getStreet() as $street) {
                $streets[] = Data::ANONYMOUS_STR;
            }

            $address->setStreet($streets);

            try {
                $this->addressResourceModel->save($address);
            } catch (\Exception $e) {
                $this->logger->notice($e->getMessage());
            }
        }
    }
}
