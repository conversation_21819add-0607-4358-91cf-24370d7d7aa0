<?php

namespace CopeX\CleanCustomers\Model\Privacy\Customer;

use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Catalog\Model\ResourceModel\Product\Compare\Item\CollectionFactory;
use Magento\Store\Model\StoreManagerInterface;

class Compare implements PrivacyInterface
{
    private CollectionFactory $collectionFactory;

    private StoreManagerInterface $storeManager;

    public function __construct(
        CollectionFactory $collection,
        StoreManagerInterface $storeManager
    ) {
        $this->collectionFactory = $collection;
        $this->storeManager = $storeManager;
    }

    /**
     * Executed upon customer product compare data deletion.
     *
     * @param $customer
     */
    public function delete($customer): void
    {
        $this->processCompare($customer->getId());
    }

    /**
     * Executed upon customer product compare data anonymization.
     *
     * @param $customer
     */
    public function anonymize($customer): void
    {
        $this->processCompare($customer->getId());
    }

    /**
     * Process product compare deletion.
     *
     * @param $customerId
     */
    protected function processCompare($customerId): void
    {
        foreach ($this->storeManager->getStores() as $store) {
            $this->collectionFactory->create()
                ->setStoreId($store->getId())
                ->setCustomerId($customerId)
                ->setVisibility([
                    Visibility::VISIBILITY_IN_SEARCH,
                    Visibility::VISIBILITY_IN_CATALOG,
                    Visibility::VISIBILITY_BOTH,
                ])
                ->delete();
        }
    }
}
