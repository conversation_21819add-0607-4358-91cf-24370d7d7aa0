<?php

namespace CopeX\CleanCustomers\Model\Privacy\Customer;

use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;
use Magento\Wishlist\Model\ResourceModel\Wishlist\CollectionFactory;

class Wishlist implements PrivacyInterface
{
    private CollectionFactory $collectionFactory;

    public function __construct(
        CollectionFactory $collectionFactory
    ) {
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * Executed upon customer wishlist data deletion.
     *
     * @param $customer
     */
    public function delete($customer): void
    {
        $this->processWishlist($customer->getId());
    }

    /**
     * Executed upon customer wishlist data anonymization.
     *
     * @param $customer
     */
    public function anonymize($customer): void
    {
        $this->processWishlist($customer->getId());
    }

    /**
     * Process wishlist deletion.
     *
     * @param $customerId
     */
    protected function processWishlist($customerId): void
    {
        $this->collectionFactory->create()
            ->addFieldToFilter('customer_id', $customerId)
            ->walk('delete');
    }
}
