<?php

namespace CopeX\CleanCustomers\Model\Privacy\Customer;

use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;
use Magento\Quote\Model\ResourceModel\Quote\CollectionFactory;

class Quote implements PrivacyInterface
{
    private CollectionFactory $collectionFactory;

    public function __construct(
        CollectionFactory $collectionFactory
    ) {
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * Executed upon customer quote data deletion.
     *
     * @param $customer
     */
    public function delete($customer): void
    {
        $this->processQuote($customer->getId());
    }

    /**
     * Executed upon customer quote data anonymization.
     *
     * @param $customer
     */
    public function anonymize($customer): void
    {
        $this->processQuote($customer->getId());
    }

    /**
     * Process quote deletion.
     *
     * @param $customerId
     */
    protected function processQuote($customerId): void
    {
        $this->collectionFactory->create()
            ->addFieldToFilter('is_active', 1)
            ->addFieldToFilter('customer_id', $customerId)
            ->walk('delete');
    }
}
