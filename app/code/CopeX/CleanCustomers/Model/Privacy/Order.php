<?php

namespace CopeX\CleanCustomers\Model\Privacy;

use CopeX\CleanCustomers\Helper\Data;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;

class Order extends AbstractModel
{
    private OrderCollectionFactory $orderCollectionFactory;

    public function __construct(
        Data $helper,
        OrderCollectionFactory $orderCollectionFactory,
        array $processors = []
    ) {
        parent::__construct($helper, $processors);
        $this->orderCollectionFactory = $orderCollectionFactory;
    }

    /**
     * Collects all orders which have to be cleaned up and processes clean up dependent on the configured cleanup mode (behavior).
     */
    public function execute(): void
    {
        $behavior = $this->helper->getGeneralConfig(Data::XML_ORDERS, false);

        if ($behavior) {
            $formattedDateOrder = $this->helper->getOrderCleanDate();
            $orders = $this->orderCollectionFactory->create();
            $orders->addFieldToFilter(
                ['customer_firstname', 'customer_firstname'],
                [['neq' => Data::ANONYMOUS_STR], ['null' => true]]
            )
                ->addFieldToFilter('customer_id', ['null' => true])
                ->addFieldToFilter('created_at', ['lt' => $formattedDateOrder]);

            foreach ($orders as $order) {
                $this->processItem($order, $behavior);
            }
        }
    }
}
