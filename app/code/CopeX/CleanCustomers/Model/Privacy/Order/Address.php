<?php

namespace CopeX\CleanCustomers\Model\Privacy\Order;

use CopeX\CleanCustomers\Helper\Data;
use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\ResourceModel\Order\Address as AddressResourceModel;
use Psr\Log\LoggerInterface;

class Address implements PrivacyInterface
{
    private Data $helper;

    private AddressResourceModel $addressResourceModel;

    private LoggerInterface $logger;

    public function __construct(
        Data $helper,
        AddressResourceModel $addressResourceModel,
        LoggerInterface $logger
    ) {
        $this->helper = $helper;
        $this->addressResourceModel = $addressResourceModel;
        $this->logger = $logger;
    }

    /**
     * Executed upon order address data deletion.
     *
     * @param $order Order
     */
    public function delete($order): void
    {
        if ($order instanceof Order) {
            $order->getAddressesCollection()->walk('delete');
        }
    }

    /**
     * Executed upon order address data anonymization.
     *
     * @param $order Order
     */
    public function anonymize($order): void
    {
        if ($order instanceof Order) {
            foreach ($order->getAddressesCollection() as $address) {
                $address
                    ->setPrefix(Data::ANONYMOUS_STR)
                    ->setLastname(Data::ANONYMOUS_STR)
                    ->setFirstname(Data::ANONYMOUS_STR)
                    ->setSuffix(Data::ANONYMOUS_STR)
                    ->setPostcode(Data::ANONYMOUS_STR)
                    ->setStreet(Data::ANONYMOUS_STR)
                    ->setCity(Data::ANONYMOUS_STR)
                    ->setEmail($this->helper->getAnonymousEmail($address->getId()))
                    ->setTelephone(Data::ANONYMOUS_STR);

                try {
                    $this->addressResourceModel->save($address);
                } catch (\Exception $e) {
                    $this->logger->notice($e->getMessage());
                }
            }
        }
    }
}
