<?php

namespace CopeX\CleanCustomers\Model\Privacy\Order;

use CopeX\CleanCustomers\Helper\Data as HelperData;
use CopeX\CleanCustomers\Model\Privacy\PrivacyInterface;
use Magento\Sales\Model\ResourceModel\Order;
use Psr\Log\LoggerInterface;

class Data implements PrivacyInterface
{
    private Order $orderResourceModel;

    private HelperData $helper;

    private LoggerInterface $logger;

    /**
     * Order constructor.
     */
    public function __construct(
        Order $orderResourceModel,
        HelperData $helper,
        LoggerInterface $logger
    ) {
        $this->orderResourceModel = $orderResourceModel;
        $this->helper = $helper;
        $this->logger = $logger;
    }

    /**
     * Executed upon order data deletion.
     *
     * @param $order \Magento\Sales\Model\Order
     */
    public function delete($order): void
    {
        if ($order instanceof \Magento\Sales\Model\Order) {
            try {
                $this->orderResourceModel->delete($order);
            } catch (\Exception $e) {
                $this->logger->notice($e->getMessage());
            }
        }
    }

    /**
     * Executed upon order data anonymization.
     *
     * @param $order \Magento\Sales\Model\Order
     */
    public function anonymize($order): void
    {
        if ($order instanceof \Magento\Sales\Model\Order) {
            $order->setCustomerEmail($this->helper->getAnonymousEmail($order->getId()))
                ->setCustomerFirstname(HelperData::ANONYMOUS_STR)
                ->setCustomerLastname(HelperData::ANONYMOUS_STR)
                ->setCustomerMiddlename(HelperData::ANONYMOUS_STR)
                ->setRemoteIp('127.0.0.1');

            try {
                $this->orderResourceModel->save($order);
            } catch (\Exception $e) {
                $this->logger->notice($e->getMessage());
            }
        }
    }
}
