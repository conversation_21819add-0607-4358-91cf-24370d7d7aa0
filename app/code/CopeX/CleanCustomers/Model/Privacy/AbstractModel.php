<?php

namespace Cope<PERSON>\CleanCustomers\Model\Privacy;

use CopeX\CleanCustomers\Helper\Data;
use CopeX\CleanCustomers\Model\Config\Source\Schema;

abstract class AbstractModel implements ModelInterface
{
    protected Data $helper;

    /** @var array<string> */
    private array $processors;

    private bool $dryRun = false;

    private array $dryRunMessages = [];

    /**
     * Constructor
     */
    public function __construct(
        Data $helper,
        array $processors = []
    ) {
        $this->processors = $processors;
        $this->helper = $helper;
    }

    /**
     * Get available processors
     *
     * @return array<string>
     */
    public function getProcessors(): array
    {
        return $this->processors;
    }

    /**
     * Activates dry run mode for clean up process execution.
     */
    public function dryRun()
    {
        $this->dryRun = true;
        $this->execute();
        return $this->dryRunMessages;
    }

    /**
     * Trigger customer clean up processors
     *
     * @param $object
     * @param $type
     */
    public function processItem($object, $type, null $processors = null): void
    {
        if (! $processors) {
            $processors = $this->processors;
        }

        if ($this->dryRun) {
            $this->dryRunMessages[] = $object::class . ': ' . $object->getId();
        } else {
            foreach ($processors as $processor) {
                switch ($type) {
                    case Schema::DELETE:
                        $processor->delete($object);
                        break;

                    case Schema::ANONYMIZE:
                        $processor->anonymize($object);
                        break;
                }
            }
        }
    }
}
