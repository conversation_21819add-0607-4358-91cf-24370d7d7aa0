<?php

namespace CopeX\CleanCustomers\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class Unit implements OptionSourceInterface
{
    /**
     * Get configuration options for possible time span units (year, month) as value and label array.
     *
     * @return array<array>
     */
    public function toOptionArray(): array
    {
        return [
            [
                'value' => 'Y',
                'label' => __('Year(s)'),
            ],
            [
                'value' => 'M',
                'label' => __('Month(s)'),
            ],
        ];
    }

    /**
     * Get configuration options for possible time span units (year, month) as array.
     */
    public function toArray(): array
    {
        return [
            'Y' => __('Year(s)'),
            'M' => __('Month(s)'),
        ];
    }
}
