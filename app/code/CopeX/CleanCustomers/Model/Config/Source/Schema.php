<?php

namespace CopeX\CleanCustomers\Model\Config\Source;

class Schema
{
    public const NONE = 0;
    public const DELETE = 1;
    public const ANONYMIZE = 2;

    /**
     * Get configuration options for possible cleanup modes.
     */
    public function getOptions(): array
    {
        return [
            self::NONE => __('Clean up nothing'),
            self::DELETE => __('Always delete'),
            self::ANONYMIZE => __('Always anonymize'),
        ];
    }

    /**
     * Get configuration options for possible cleanup modes as value and label array.
     */
    public function toOptionArray(): array
    {
        $options = [];

        foreach ($this->getOptions() as $key => $value) {
            $options[] = ['value' => $key, 'label' => $value];
        }

        return $options;
    }

    /**
     * Get configuration options for possible cleanup modes as array.
     */
    public function toArray(): array
    {
        return $this->getOptions();
    }
}
