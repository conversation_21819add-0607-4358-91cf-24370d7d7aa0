<?php

namespace CopeX\HyvaTheme\ViewModel;

use Magento\Cms\Api\Data\BlockInterface;
use Magento\Cms\Model\BlockFactory;
use Magento\Cms\Model\ResourceModel\Block;
use Magento\Cms\Model\Template\FilterProvider;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\StoreManagerInterface;

class Cms implements ArgumentInterface
{
    private $blockCache = [];
    private FilterProvider $filterProvider;
    private StoreManagerInterface $storeManager;
    private BlockFactory $blockFactory;
    private Block $blockResource;

    public function __construct(
        FilterProvider $filterProvider,
        BlockFactory $blockFactory,
        Block $blockResource,
        StoreManagerInterface $storeManager
    ) {
        $this->blockFactory = $blockFactory;
        $this->blockResource = $blockResource;
        $this->filterProvider = $filterProvider;
        $this->storeManager = $storeManager;
    }

    public function renderBlockByIdentifier($identifier, $fieldToLoad = BlockInterface::IDENTIFIER)
    {
        if (! isset($this->blockCache[$identifier])) {
            try {
                $content = $this->getBlockBy($identifier, $fieldToLoad)->getContent();
                $this->blockCache[$identifier] = $this->renderBlockContent($content);
            } catch (\Exception $e) {
                $this->blockCache[$identifier] = '';
            }
        }
        return $this->blockCache[$identifier];
    }

    public function renderBlockContent($content = '')
    {
        if ($content) {
            return $this->filterProvider->getBlockFilter()->filter(trim($content));
        }
        return $content;
    }

    private function getBlockBy($identifier, $field = BlockInterface::IDENTIFIER)
    {
        $block = $this->blockFactory->create();
        $block->setStoreId($this->storeManager->getStore()->getId());
        $this->blockResource->load($block, $identifier, $field);
        if (! $block->getId()) {
            throw new NoSuchEntityException(__('The CMS block with the "%1" ID doesn\'t exist.', $identifier));
        }

        return $block;
    }
}
