<?php

declare(strict_types=1);

namespace CopeX\HyvaTheme\ViewModel;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\StoreManagerInterface;

class Footer implements ArgumentInterface
{
    public const LOGO_PATH = 'design/header/logo_src';

    public const LOGO_ALT = 'general/store_information/name';

    private StoreManagerInterface $storeManager;

    public function __construct(
        StoreManagerInterface $storeManager
    ) {
        $this->storeManager = $storeManager;
    }

    public function getStoreLogoUrl(): string
    {
        try {
            $store = $this->storeManager->getStore();
            $mediaUrl = $store->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            return $mediaUrl . 'logo/' . $store->getConfig(self::LOGO_PATH);
        } catch (NoSuchEntityException $e) {
            return '';
        }
    }

    public function getStoreLogoAlt()
    {
        try {
            $store = $this->storeManager->getStore();
            return $store->getConfig(self::LOGO_ALT);
        } catch (NoSuchEntityException $e) {
            return __('Store logo');
        }
    }

    public function getHomeUrl(): string
    {
        try {
            $store = $this->storeManager->getStore();
            return $store->getBaseUrl();
        } catch (NoSuchEntityException $e) {
            return DIRECTORY_SEPARATOR;
        }
    }
}
