<?php

namespace CopeX\HyvaTheme\ViewModel;

use Magento\Framework\App\RequestInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class Request implements ArgumentInterface
{

    private RequestInterface $request;


    public function __construct(RequestInterface $request)
    {
        $this->request = $request;
    }

    public function isListView(){
        return $this->request->getFullActionName() == "catalog_category_view" || $this->request->getFullActionName() == "catalogsearch_result_index";
    }

    public function getFullActionName(){
        return $this->request->getFullActionName();
    }
}
