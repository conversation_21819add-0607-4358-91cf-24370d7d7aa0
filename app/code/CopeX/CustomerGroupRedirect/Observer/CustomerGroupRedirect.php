<?php
declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Observer;

use CopeX\CustomerGroupRedirect\Helper\Data;
use Exception;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\ActionFlag;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\App\Response\RedirectInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Store\Api\Data\StoreInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class CustomerGroupRedirect implements ObserverInterface
{
    private CustomerSession $customerSession;
    private StoreManagerInterface $storeManager;
    private Data $helper;
    private RedirectInterface $redirect;
    private ActionFlag $actionFlag;
    private ResponseInterface $response;
    private LoggerInterface $logger;

    public function __construct(
        CustomerSession $customerSession,
        StoreManagerInterface $storeManager,
        Data $helper,
        RedirectInterface $redirect,
        ActionFlag $actionFlag,
        ResponseInterface $response,
        LoggerInterface $logger
    ) {
        $this->customerSession = $customerSession;
        $this->storeManager = $storeManager;
        $this->helper = $helper;
        $this->redirect = $redirect;
        $this->actionFlag = $actionFlag;
        $this->response = $response;
        $this->logger = $logger;
    }

    /**
     * Execute customer group redirect
     */
    public function execute(Observer $observer): void
    {
        try {
            // Check if we should perform redirect
            if (! $this->helper->isEnabled()) {
                return;
            }

            // Don't redirect AJAX requests
            if ($observer->getEvent()->getRequest() && $observer->getEvent()->getRequest()->isXmlHttpRequest()) {
                return;
            }

            // Don't redirect POST requests
            if ($observer->getEvent()->getRequest() && $observer->getEvent()->getRequest()->isPost()) {
                return;
            }

            // Don't redirect admin or API requests
            $requestUri = $observer->getEvent()->getRequest() ? $observer->getEvent()->getRequest()->getRequestUri() : '';
            if (str_starts_with($requestUri, '/admin') ||
                str_starts_with($requestUri, '/rest/') ||
                str_starts_with($requestUri, '/soap/')) {
                return;
            }

            // Get customer group
            $customerGroupId = $this->customerSession->isLoggedIn()
                ? $this->customerSession->getCustomerGroupId()
                : 0; // 0 is the NOT LOGGED IN group

            $this->helper->logDebug('Processing redirect for customer group: ' . $customerGroupId);

            // Check if we need to redirect based on customer group
            $targetStore = $this->helper->getTargetStoreForCustomerGroup((int) $customerGroupId);

            if (! $targetStore) {
                $this->helper->logDebug('No target store found for customer group: ' . $customerGroupId);
                return;
            }

            // Get current store
            $currentStore = $this->storeManager->getStore();

            // If current store already matches target store, return
            if ($currentStore->getId() === $targetStore->getId()) {
                $this->helper->logDebug('Current store already matches target store for customer group');
                return;
            }

            // Perform redirect
            $this->performRedirect($targetStore, $observer);
        } catch (Exception $e) {
            $this->logger->error('Error in customer group redirect: ' . $e->getMessage());
        }
    }

    /**
     * Perform the actual redirect to target store
     */
    private function performRedirect(StoreInterface $targetStore, Observer $observer): void
    {
        $request = $observer->getEvent()->getRequest();

        if (! $request) {
            return;
        }

        // Build target URL
        $currentUrl = $request->getRequestUri();
        $targetBaseUrl = $targetStore->getBaseUrl();

        // Remove any existing store code from URL
        $cleanUrl = $this->cleanUrlFromStoreCode($currentUrl);
        $targetUrl = rtrim($targetBaseUrl, '/') . $cleanUrl;

        $this->helper->logDebug('Redirecting to: ' . $targetUrl);

        // Set redirect response with 302 status code to prevent browser caching
        $this->response->setRedirect($targetUrl);

        // Stop further processing
        $this->actionFlag->set('', ActionInterface::FLAG_NO_DISPATCH, true);
    }

    /**
     * Clean URL from store code
     */
    private function cleanUrlFromStoreCode(string $url): string
    {
        // Remove store codes from URL if present
        $stores = $this->storeManager->getStores();
        foreach ($stores as $store) {
            $storeCode = $store->getCode();
            if (str_starts_with($url, '/' . $storeCode . '/')) {
                return substr($url, strlen('/' . $storeCode));
            }
        }

        return $url;
    }
}
