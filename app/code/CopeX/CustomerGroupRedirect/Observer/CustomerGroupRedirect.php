<?php
declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Observer;

use CopeX\CustomerGroupRedirect\Helper\Data;
use Exception;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\ActionFlag;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\Response\RedirectInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\QuoteFactory;
use Magento\Quote\Model\QuoteRepository;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Store\Api\Data\StoreInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class CustomerGroupRedirect implements ObserverInterface
{
    private CustomerSession $customerSession;
    private StoreManagerInterface $storeManager;
    private Data $helper;
    private RedirectInterface $redirect;
    private ActionFlag $actionFlag;
    private ResponseInterface $response;
    private LoggerInterface $logger;
    private RequestInterface $request;

    public function __construct(
        CustomerSession $customerSession,
        StoreManagerInterface $storeManager,
        Data $helper,
        RedirectInterface $redirect,
        ActionFlag $actionFlag,
        ResponseInterface $response,
        LoggerInterface $logger,
        RequestInterface $request
    ) {
        $this->customerSession = $customerSession;
        $this->storeManager = $storeManager;
        $this->helper = $helper;
        $this->redirect = $redirect;
        $this->actionFlag = $actionFlag;
        $this->response = $response;
        $this->logger = $logger;
        $this->request = $request;
    }

    /**
     * Execute customer group redirect
     */
    public function execute(Observer $observer): void
    {
        try {
            // Check if we should perform redirect
            if (! $this->helper->isEnabled()) {
                return;
            }

            // Don't redirect AJAX requests
            if ($this->request->isXmlHttpRequest()) {
                return;
            }

            // Don't redirect admin or API requests
            $requestUri = $this->request->getRequestUri();
            if (str_starts_with($requestUri, '/admin') ||
                str_starts_with($requestUri, '/rest/') ||
                str_starts_with($requestUri, '/soap/')) {
                return;
            }

            // Get customer group
            $customerGroupId = $this->customerSession->isLoggedIn()
                ? $this->customerSession->getCustomerGroupId()
                : 0; // 0 is the NOT LOGGED IN group

            $this->helper->logDebug('Processing redirect for customer group: ' . $customerGroupId);

            // Check if we need to redirect based on customer group
            $targetStore = $this->helper->getTargetStoreForCustomerGroup((int) $customerGroupId);

            if (! $targetStore) {
                $this->helper->logDebug('No target store found for customer group: ' . $customerGroupId);
                return;
            }

            // Get current store
            $currentStore = $this->storeManager->getStore();

            // If current store already matches target store, return
            if ($currentStore->getId() === $targetStore->getId()) {
                $this->helper->logDebug('Current store already matches target store for customer group');
                return;
            }

            // Perform redirect
            $this->performRedirect($targetStore);
        } catch (Exception $e) {
            $this->logger->error('Error in customer group redirect: ' . $e->getMessage());
        }
    }

    /**
     * Perform the actual redirect to target store
     */
    private function performRedirect(StoreInterface $targetStore): void
    {
        // Build target URL using configured website URLs
        $currentUrl = $this->request->getRequestUri();

        // Determine if we're redirecting to B2B or B2C based on target store's website
        $targetWebsiteCode = $this->storeManager->getWebsite($targetStore->getWebsiteId())->getCode();
        $b2bWebsiteCode = $this->helper->getB2bWebsiteCode();

        // Use configured URLs from backend
        if ($targetWebsiteCode === $b2bWebsiteCode) {
            $targetBaseUrl = $this->helper->getRedirectUrl(true); // B2B URL
        } else {
            $targetBaseUrl = $this->helper->getRedirectUrl(false); // B2C URL
        }

        // Remove any existing store code from URL
        $cleanUrl = $this->cleanUrlFromStoreCode($currentUrl);

        // Determine target URL based on current page type
        $targetUrl = $this->buildTargetUrl($targetBaseUrl, $cleanUrl);

        $this->helper->logDebug('Redirect details', [
            'target_website_code' => $targetWebsiteCode,
            'is_b2b_redirect' => $targetWebsiteCode === $b2bWebsiteCode,
            'configured_base_url' => $targetBaseUrl,
            'current_url' => $currentUrl,
            'clean_url' => $cleanUrl,
            'final_redirect_url' => $targetUrl,
        ]);

        // Set redirect response with 302 status code to prevent browser caching
        $this->response->setRedirect($targetUrl);
        $this->response->setHttpResponseCode(302);

        // Stop further processing
        $this->actionFlag->set('', ActionInterface::FLAG_NO_DISPATCH, true);

        // For immediate redirect, especially useful for login events
        if (! headers_sent()) {
            header('Location: ' . $targetUrl, true, 302);
            exit;
        }
    }

    /**
     * Build target URL with intelligent path handling
     */
    private function buildTargetUrl(string $baseUrl, string $currentPath): string
    {
        $baseUrl = rtrim($baseUrl, '/');

        // If configured to always redirect to homepage, do so
        if ($this->helper->shouldRedirectToHomepage()) {
            $this->helper->logDebug('Redirecting to homepage (configured behavior)', [
                'original_path' => $currentPath,
            ]);
            return $baseUrl . '/';
        }

        // List of safe paths that are likely to exist on both websites
        $safePaths = [
            '/',
            '/customer/account/',
            '/customer/account/login/',
            '/checkout/',
            '/checkout/cart/',
        ];

        // Check if current path is a safe path or starts with a safe path
        foreach ($safePaths as $safePath) {
            if ($currentPath === $safePath || str_starts_with($currentPath, $safePath)) {
                return $baseUrl . $currentPath;
            }
        }

        // For product pages, category pages, or other specific pages,
        // redirect to homepage to avoid 404 errors
        if (str_contains($currentPath, '.html') ||
            str_starts_with($currentPath, '/catalog/') ||
            str_starts_with($currentPath, '/catalogsearch/')) {
            $this->helper->logDebug('Redirecting product/category page to homepage to avoid 404', [
                'original_path' => $currentPath,
            ]);
            return $baseUrl . '/';
        }

        // For all other paths, try to preserve them but log for monitoring
        $this->helper->logDebug('Preserving URL path - monitor for 404s', [
            'path' => $currentPath,
        ]);

        return $baseUrl . $currentPath;
    }

    /**
     * Clean URL from store code
     */
    private function cleanUrlFromStoreCode(string $url): string
    {
        // Remove store codes from URL if present
        $stores = $this->storeManager->getStores();
        foreach ($stores as $store) {
            $storeCode = $store->getCode();
            if (str_starts_with($url, '/' . $storeCode . '/')) {
                return substr($url, strlen('/' . $storeCode));
            }
        }

        return $url;
    }
}
