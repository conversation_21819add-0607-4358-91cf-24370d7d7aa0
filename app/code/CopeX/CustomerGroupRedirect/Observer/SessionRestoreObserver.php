<?php
/**
 * Copyright © CopeX. All rights reserved.
 */

declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Observer;

use CopeX\CustomerGroupRedirect\Helper\Data;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

class SessionRestoreObserver implements ObserverInterface
{
    private CustomerSession $customerSession;
    private Data $helper;
    private RequestInterface $request;
    private CustomerRepositoryInterface $customerRepository;
    private LoggerInterface $logger;

    public function __construct(
        CustomerSession $customerSession,
        Data $helper,
        RequestInterface $request,
        CustomerRepositoryInterface $customerRepository,
        LoggerInterface $logger
    ) {
        $this->customerSession = $customerSession;
        $this->helper = $helper;
        $this->request = $request;
        $this->customerRepository = $customerRepository;
        $this->logger = $logger;
    }

    /**
     * Restore customer session from cross-website transfer
     */
    public function execute(Observer $observer): void
    {
        // Only process if not already logged in and we have a restore token
        if ($this->customerSession->isLoggedIn()) {
            return;
        }

        $restoreToken = $this->request->getParam('restore_session');
        if (!$restoreToken) {
            return;
        }

        try {
            // Get preserved session data
            $sessionData = $this->helper->getPreservedSessionData($restoreToken);
            if (!$sessionData) {
                $this->helper->logDebug('No session data found for restore token');
                return;
            }

            // Check if session data has expired
            if (isset($sessionData['expires_at']) && time() > $sessionData['expires_at']) {
                $this->helper->logDebug('Session restore token has expired');
                $this->helper->clearPreservedSessionData($restoreToken);
                return;
            }

            // Validate required data
            if (!isset($sessionData['customer_id'], $sessionData['customer_email'])) {
                $this->helper->logDebug('Invalid session data for restore');
                $this->helper->clearPreservedSessionData($restoreToken);
                return;
            }

            // Load and login customer
            $customerId = (int)$sessionData['customer_id'];
            $customer = $this->customerRepository->getById($customerId);
            
            if ($customer->getEmail() !== $sessionData['customer_email']) {
                $this->helper->logDebug('Customer email mismatch during session restore');
                $this->helper->clearPreservedSessionData($restoreToken);
                return;
            }

            // Restore customer session
            $this->customerSession->setCustomerDataAsLoggedIn($customer);
            
            // Clean up the temporary session data
            $this->helper->clearPreservedSessionData($restoreToken);
            
            $this->helper->logDebug('Successfully restored customer session', [
                'customer_id' => $customerId,
                'customer_email' => $customer->getEmail()
            ]);

        } catch (NoSuchEntityException $e) {
            $this->helper->logDebug('Customer not found during session restore: ' . $e->getMessage());
            $this->helper->clearPreservedSessionData($restoreToken);
        } catch (LocalizedException $e) {
            $this->helper->logDebug('Error during session restore: ' . $e->getMessage());
            $this->helper->clearPreservedSessionData($restoreToken);
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error during session restore: ' . $e->getMessage());
            $this->helper->clearPreservedSessionData($restoreToken);
        }
    }
}
