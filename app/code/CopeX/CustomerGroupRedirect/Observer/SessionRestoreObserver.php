<?php
/**
 * Copyright © CopeX. All rights reserved.
 */

declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Observer;

use CopeX\CustomerGroupRedirect\Helper\Data;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Model\QuoteFactory;
use Magento\Quote\Model\QuoteRepository;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class SessionRestoreObserver implements ObserverInterface
{
    private CustomerSession $customerSession;
    private CheckoutSession $checkoutSession;
    private Data $helper;
    private RequestInterface $request;
    private CustomerRepositoryInterface $customerRepository;
    private ProductRepositoryInterface $productRepository;
    private QuoteFactory $quoteFactory;
    private QuoteRepository $quoteRepository;
    private StoreManagerInterface $storeManager;
    private LoggerInterface $logger;

    public function __construct(
        CustomerSession $customerSession,
        CheckoutSession $checkoutSession,
        Data $helper,
        RequestInterface $request,
        CustomerRepositoryInterface $customerRepository,
        ProductRepositoryInterface $productRepository,
        QuoteFactory $quoteFactory,
        QuoteRepository $quoteRepository,
        StoreManagerInterface $storeManager,
        LoggerInterface $logger
    ) {
        $this->customerSession = $customerSession;
        $this->checkoutSession = $checkoutSession;
        $this->helper = $helper;
        $this->request = $request;
        $this->customerRepository = $customerRepository;
        $this->productRepository = $productRepository;
        $this->quoteFactory = $quoteFactory;
        $this->quoteRepository = $quoteRepository;
        $this->storeManager = $storeManager;
        $this->logger = $logger;
    }

    /**
     * Restore customer session and cart from cross-website transfer
     */
    public function execute(Observer $observer): void
    {
        // Only process if not already logged in and we have a restore token
        if ($this->customerSession->isLoggedIn()) {
            return;
        }

        $restoreToken = $this->request->getParam('restore_session');
        if (! $restoreToken) {
            return;
        }

        try {
            // Restore customer session first
            $sessionRestored = $this->restoreCustomerSession($restoreToken);

            // If session was restored and cart preservation is enabled, restore cart
            if ($sessionRestored && $this->helper->isCartPreservationEnabled()) {
                $this->restoreCart($restoreToken);
            }
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error during session/cart restore: ' . $e->getMessage());
            $this->helper->clearPreservedSessionData($restoreToken);
            $this->helper->clearPreservedCartData($restoreToken);
        }
    }

    /**
     * Restore customer session
     */
    private function restoreCustomerSession(string $restoreToken): bool
    {
        try {
            // Get preserved session data
            $sessionData = $this->helper->getPreservedSessionData($restoreToken);
            if (! $sessionData) {
                $this->helper->logDebug('No session data found for restore token');
                return false;
            }

            // Check if session data has expired
            if (isset($sessionData['expires_at']) && time() > $sessionData['expires_at']) {
                $this->helper->logDebug('Session restore token has expired');
                $this->helper->clearPreservedSessionData($restoreToken);
                return false;
            }

            // Validate required data
            if (! isset($sessionData['customer_id'], $sessionData['customer_email'])) {
                $this->helper->logDebug('Invalid session data for restore');
                $this->helper->clearPreservedSessionData($restoreToken);
                return false;
            }

            // Load and login customer
            $customerId = (int) $sessionData['customer_id'];
            $customer = $this->customerRepository->getById($customerId);

            if ($customer->getEmail() !== $sessionData['customer_email']) {
                $this->helper->logDebug('Customer email mismatch during session restore');
                $this->helper->clearPreservedSessionData($restoreToken);
                return false;
            }

            // Restore customer session
            $this->customerSession->setCustomerDataAsLoggedIn($customer);

            // Clean up the temporary session data
            $this->helper->clearPreservedSessionData($restoreToken);

            $this->helper->logDebug('Successfully restored customer session', [
                'customer_id' => $customerId,
                'customer_email' => $customer->getEmail(),
            ]);

            return true;
        } catch (NoSuchEntityException $e) {
            $this->helper->logDebug('Customer not found during session restore: ' . $e->getMessage());
            $this->helper->clearPreservedSessionData($restoreToken);
            return false;
        } catch (LocalizedException $e) {
            $this->helper->logDebug('Error during session restore: ' . $e->getMessage());
            $this->helper->clearPreservedSessionData($restoreToken);
            return false;
        }
    }

    /**
     * Restore cart items
     */
    private function restoreCart(string $restoreToken): void
    {
        try {
            // Get preserved cart data
            $cartData = $this->helper->getPreservedCartData($restoreToken);
            if (! $cartData) {
                $this->helper->logDebug('No cart data found for restore token');
                return;
            }

            // Check if cart data has expired
            if (isset($cartData['expires_at']) && time() > $cartData['expires_at']) {
                $this->helper->logDebug('Cart restore token has expired');
                $this->helper->clearPreservedCartData($restoreToken);
                return;
            }

            // Validate cart data
            if (! isset($cartData['items']) || ! is_array($cartData['items'])) {
                $this->helper->logDebug('Invalid cart data for restore');
                $this->helper->clearPreservedCartData($restoreToken);
                return;
            }

            // Get or create quote for current store
            $quote = $this->checkoutSession->getQuote();
            if (! $quote->getId()) {
                $quote = $this->quoteFactory->create();
                $quote->setStore($this->storeManager->getStore());
                if ($this->customerSession->isLoggedIn()) {
                    $quote->setCustomer($this->customerSession->getCustomer());
                }
            }

            $itemsAdded = 0;
            foreach ($cartData['items'] as $itemData) {
                try {
                    if (! isset($itemData['product_id'], $itemData['qty'])) {
                        continue;
                    }

                    $product = $this->productRepository->getById(
                        $itemData['product_id'],
                        false,
                        $this->storeManager->getStore()->getId()
                    );

                    if ($product && $product->isSaleable()) {
                        $quote->addProduct($product, (float) $itemData['qty']);
                        $itemsAdded++;
                    }
                } catch (\Exception $e) {
                    $this->helper->logDebug('Failed to restore cart item: ' . $e->getMessage(), [
                        'product_id' => $itemData['product_id'] ?? 'unknown',
                    ]);
                }
            }

            if ($itemsAdded > 0) {
                $quote->collectTotals();
                $this->quoteRepository->save($quote);
                $this->checkoutSession->setQuoteId($quote->getId());

                $this->helper->logDebug('Successfully restored cart items', [
                    'items_added' => $itemsAdded,
                    'total_items' => count($cartData['items']),
                ]);
            }

            // Clean up the temporary cart data
            $this->helper->clearPreservedCartData($restoreToken);
        } catch (\Exception $e) {
            $this->helper->logDebug('Error during cart restore: ' . $e->getMessage());
            $this->helper->clearPreservedCartData($restoreToken);
        }
    }
}
