<?php
declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Helper;

use Exception;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Session\SessionManagerInterface;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Magento\Framework\Stdlib\Cookie\CookieSizeLimitReachedException;
use Magento\Framework\Stdlib\Cookie\FailureToSendException;
use Magento\Framework\Stdlib\CookieManagerInterface;
use Magento\Store\Api\Data\StoreInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class Data extends AbstractHelper
{
    public const COOKIE_NAME = 'copex_group_redirect_processed';
    public const CONFIG_PATH_ENABLED = 'copex_customergroupredirect/general/enabled';
    public const CONFIG_PATH_PRESERVE_CART = 'copex_customergroupredirect/general/preserve_cart';
    public const CONFIG_PATH_DEBUG = 'copex_customergroupredirect/general/debug';
    public const CONFIG_PATH_GROUP_MAPPING = 'copex_customergroupredirect/group_mapping/';
    public const SESSION_QUOTE_ID_KEY = 'copex_preserved_quote_id';
    public const SESSION_REDIRECT_PROCESSED = 'copex_redirect_processed';

    private StoreManagerInterface $storeManager;
    private CookieManagerInterface $cookieManager;
    private CookieMetadataFactory $cookieMetadataFactory;
    private SessionManagerInterface $sessionManager;
    private LoggerInterface $logger;

    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        CookieManagerInterface $cookieManager,
        CookieMetadataFactory $cookieMetadataFactory,
        SessionManagerInterface $sessionManager,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->storeManager = $storeManager;
        $this->cookieManager = $cookieManager;
        $this->cookieMetadataFactory = $cookieMetadataFactory;
        $this->sessionManager = $sessionManager;
        $this->logger = $logger;
    }

    /**
     * Check if module is enabled
     */
    public function isEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::CONFIG_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Check if cart preservation is enabled
     */
    public function isCartPreservationEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::CONFIG_PATH_PRESERVE_CART,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Check if debug mode is enabled
     */
    public function isDebugEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::CONFIG_PATH_DEBUG,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get target store for customer group
     */
    public function getTargetStoreForCustomerGroup(int $customerGroupId): ?StoreInterface
    {
        try {
            // Get store ID from configuration for this customer group
            $targetStoreId = $this->scopeConfig->getValue(
                self::CONFIG_PATH_GROUP_MAPPING . 'group_' . $customerGroupId,
                ScopeInterface::SCOPE_STORE
            );

            if (! $targetStoreId) {
                return null;
            }

            // Get store by ID
            return $this->storeManager->getStore($targetStoreId);
        } catch (Exception $e) {
            $this->logger->error('Error getting target store for customer group: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Mark redirection as processed for current session
     */
    public function markRedirectProcessed(): void
    {
        // Use both cookie and session for redundancy

        // Set cookie
        $metadata = $this->cookieMetadataFactory->createPublicCookieMetadata()
            ->setDuration(3600) // 1 hour
            ->setPath('/')
            ->setHttpOnly(false);

        try {
            $this->cookieManager->setPublicCookie(
                self::COOKIE_NAME,
                '1',
                $metadata
            );
        } catch (InputException|CookieSizeLimitReachedException|FailureToSendException $e) {
        }

        // Set session flag
        $this->sessionManager->setData(self::SESSION_REDIRECT_PROCESSED, true);

        $this->logDebug('Marked redirect as processed');
    }

    /**
     * Check if redirection has been processed
     */
    public function isRedirectProcessed(): bool
    {
        // Check both cookie and session for redundancy
        $cookieValue = $this->cookieManager->getCookie(self::COOKIE_NAME);
        $sessionValue = $this->sessionManager->getData(self::SESSION_REDIRECT_PROCESSED);

        return $cookieValue || $sessionValue;
    }

    /**
     * Store quote ID to be preserved during redirection
     */
    public function setPreservedQuoteId(int $quoteId): void
    {
        $this->sessionManager->setData(self::SESSION_QUOTE_ID_KEY, $quoteId);
    }

    /**
     * Get preserved quote ID
     */
    public function getPreservedQuoteId(): ?int
    {
        return $this->sessionManager->getData(self::SESSION_QUOTE_ID_KEY);
    }

    /**
     * Clear preserved quote ID
     */
    public function clearPreservedQuoteId(): void
    {
        $this->sessionManager->unsetData(self::SESSION_QUOTE_ID_KEY);
    }

    /**
     * Log debug information
     */
    public function logDebug(string $message, array $context = []): void
    {
        if ($this->isDebugEnabled()) {
            $this->logger->debug('[CopeX_CustomerGroupRedirect] ' . $message, $context);
        }
    }
}
