<?php
declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Plugin;

use CopeX\CustomerGroupRedirect\Helper\Data;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\Response\Http as ResponseHttp;
use Magento\Framework\Session\SessionManagerInterface;

class SessionTransfer
{
    private CustomerSession $customerSession;
    private SessionManagerInterface $sessionManager;
    private Data $helper;
    private RequestInterface $request;

    public function __construct(
        CustomerSession $customerSession,
        SessionManagerInterface $sessionManager,
        Data $helper,
        RequestInterface $request
    ) {
        $this->customerSession = $customerSession;
        $this->sessionManager = $sessionManager;
        $this->helper = $helper;
        $this->request = $request;
    }

    /**
     * Before sending response, add session transfer parameters to redirect URL if needed
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function beforeRedirect(ResponseHttp $subject, string $url): array
    {
        // Don't modify URLs for AJAX requests
        if ($this->request->isXmlHttpRequest()) {
            return [$url];
        }

        // Add session ID to URL if customer is logged in
        if ($this->customerSession->isLoggedIn()) {
            $sessionId = $this->sessionManager->getSessionId();
            if ($sessionId) {
                $separator = str_contains($url, '?') ? '&' : '?';
                $url .= $separator . 'SID=' . urlencode($sessionId);
                $this->helper->logDebug('Added session ID to redirect URL');
            }
        }

        return [$url];
    }
}
