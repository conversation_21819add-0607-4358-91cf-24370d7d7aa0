<?php
/**
 * Copyright © CopeX. All rights reserved.
 */

declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Controller\Test;

use CopeX\CustomerGroupRedirect\Helper\Data;
use Exception;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Store\Model\StoreManagerInterface;

class Config implements HttpGetActionInterface
{
    private JsonFactory $jsonFactory;
    private Data $helper;
    private CustomerSession $customerSession;
    private StoreManagerInterface $storeManager;

    public function __construct(
        JsonFactory $jsonFactory,
        Data $helper,
        CustomerSession $customerSession,
        StoreManagerInterface $storeManager
    ) {
        $this->jsonFactory = $jsonFactory;
        $this->helper = $helper;
        $this->customerSession = $customerSession;
        $this->storeManager = $storeManager;
    }

    public function execute(): Json
    {
        $result = $this->jsonFactory->create();

        try {
            $customerGroupId = $this->customerSession->isLoggedIn()
                ? $this->customerSession->getCustomerGroupId()
                : 0;

            $currentStore = $this->storeManager->getStore();
            $currentWebsite = $this->storeManager->getWebsite();
            $defaultStore = $currentWebsite->getDefaultStore();

            $data = [
                'module_enabled' => $this->helper->isEnabled(),
                'cart_preservation_enabled' => $this->helper->isCartPreservationEnabled(),
                'debug_enabled' => $this->helper->isDebugEnabled(),
                'redirect_to_homepage' => $this->helper->shouldRedirectToHomepage(),
                'current_customer_group_id' => $customerGroupId,
                'current_store_id' => (int)$currentStore->getId(),
                'current_store_code' => $currentStore->getCode(),
                'current_website_id' => (int)$currentWebsite->getId(),
                'current_website_code' => $currentWebsite->getCode(),
                'default_store_id' => $defaultStore ? (int)$defaultStore->getId() : null,
                'default_store_code' => $defaultStore?->getCode(),
                'b2b_customer_groups' => $this->helper->getB2bCustomerGroups(),
                'is_b2b_customer' => $this->helper->isB2bCustomerGroup($customerGroupId),
                'b2b_website_code' => $this->helper->getB2bWebsiteCode(),
                'b2c_website_code' => $this->helper->getB2cWebsiteCode(),
                'b2b_url' => $this->helper->getRedirectUrl(true),
                'b2c_url' => $this->helper->getRedirectUrl(false),
                'store_mapping' => $this->helper->getStoreMapping(),
                'target_store' => null,
            ];

            // Get target store
            $targetStore = $this->helper->getTargetStoreForCustomerGroup($customerGroupId);
            if ($targetStore) {
                $data['target_store'] = [
                    'id' => $targetStore->getId(),
                    'code' => $targetStore->getCode(),
                    'name' => $targetStore->getName(),
                    'website_id' => $targetStore->getWebsiteId()
                ];
            }

            return $result->setData([
                'success' => true,
                'data' => $data
            ]);

        } catch (Exception $e) {
            return $result->setData([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
