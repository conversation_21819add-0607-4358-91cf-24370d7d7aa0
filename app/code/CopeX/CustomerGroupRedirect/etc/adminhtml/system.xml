<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="copex" translate="label" sortOrder="200">
            <label>CopeX</label>
        </tab>
        <section id="copex_customergroupredirect" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Customer Group Redirect</label>
            <tab>copex</tab>
            <resource>CopeX_CustomerGroupRedirect::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Customer Group Redirect</label>
                    <comment>Enable or disable automatic customer group-based redirection</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="preserve_cart" translate="label comment" type="select" sortOrder="7" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Preserve Shopping Cart</label>
                    <comment>Maintain cart items during redirection</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="customer_groups" translate="label comment" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>B2B Customer Groups</label>
                    <comment>Select customer groups that should be redirected to B2B website</comment>
                    <source_model>Magento\Customer\Model\Config\Source\Group</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="b2b_website_code" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>B2B Website Code</label>
                    <comment>Enter the website code for B2B (e.g., b2b_website)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="b2c_website_code" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>B2C Website Code</label>
                    <comment>Enter the website code for B2C (e.g., base)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="b2b_url" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>B2B Website URL</label>
                    <comment>Enter the base URL for B2B website (e.g., https://b2b.example.com)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="b2c_url" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>B2C Website URL</label>
                    <comment>Enter the base URL for B2C website (e.g., https://www.example.com)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
            <group id="store_mapping" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Store Mapping</label>
                <depends>
                    <field id="general/enabled">1</field>
                </depends>
                <field id="store_pairs" translate="label comment" type="textarea" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Store ID Mapping</label>
                    <comment>Enter store ID mappings in format: b2c_store_id:b2b_store_id, one pair per line</comment>
                </field>
            </group>
        </section>
    </system>
</config>
