<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Plugin to preserve cart before redirect -->
    <type name="CopeX\CustomerGroupRedirect\Observer\CustomerGroupRedirect">
        <arguments>
            <argument name="customerSession" xsi:type="object">Magento\Customer\Model\Session\Proxy</argument>
        </arguments>
    </type>

    <!-- Plugin to handle session transfer between websites -->
    <type name="Magento\Framework\App\Response\Http">
        <plugin name="copex_customergroup_redirect_session_transfer"
                type="CopeX\CustomerGroupRedirect\Plugin\SessionTransfer"
                sortOrder="10" />
    </type>

    <!-- Preference for logger -->
    <type name="CopeX\CustomerGroupRedirect\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="CopeX\CustomerGroupRedirect\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">copexCustomerGroupRedirect</argument>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">CopeX\CustomerGroupRedirect\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
    <type name="CopeX\CustomerGroupRedirect\Observer\CustomerSessionInit">
        <arguments>
            <argument name="checkoutSession" xsi:type="object">Magento\Checkout\Model\Session\Proxy</argument>
            <argument name="customerSession" xsi:type="object">Magento\Customer\Model\Session\Proxy</argument>
        </arguments>
    </type>
    <type name="CopeX\CustomerGroupRedirect\Plugin\SessionTransfer">
        <arguments>
            <argument name="customerSession" xsi:type="object">Magento\Customer\Model\Session\Proxy</argument>
        </arguments>
    </type>
    <type name="CopeX\CustomerGroupRedirect\Controller\Test\Config">
        <arguments>
            <argument name="customerSession" xsi:type="object">Magento\Customer\Model\Session\Proxy</argument>
        </arguments>
    </type>
</config>
