<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <!-- Only trigger on specific controller actions to reduce chances of redirect loops -->
    <event name="controller_action_predispatch_cms_index_index">
        <observer name="copex_customer_group_redirect_homepage"
                  instance="CopeX\CustomerGroupRedirect\Observer\CustomerGroupRedirect"/>
    </event>
    <event name="controller_action_predispatch_cms_page_view">
        <observer name="copex_customer_group_redirect_cms"
                  instance="CopeX\CustomerGroupRedirect\Observer\CustomerGroupRedirect"/>
    </event>
    <event name="customer_login">
        <observer name="copex_customer_group_redirect_login"
                  instance="CopeX\CustomerGroupRedirect\Observer\CustomerGroupRedirect"/>
    </event>
    <!-- Session restoration for cross-website transfers -->
    <event name="controller_action_predispatch">
        <observer name="copex_customer_group_redirect_session_restore"
                  instance="CopeX\CustomerGroupRedirect\Observer\SessionRestoreObserver"/>
    </event>
</config>
