<?php

namespace CopeX\AllTopProductOptions\Helper;

use Magento\Framework\App\Helper\AbstractHelper;

class OptionTitle extends AbstractHelper
{
    /**
     * Returns a formatted title text with bold tags instead of asterisks
     *
     * @param string $title
     * @return string
     */
    public function getFormattedTitleText(string $title): string
    {
        $titleText = $this->getTitleText($title);
        return $this->convertAsteriskToBoldTag($titleText);
    }

    /**
     * Returns plain title text without asterisks
     *
     * @param string $title
     * @return string
     */
    public function getPlainTitleText(string $title): string
    {
        $titleText = $this->getTitleText($title);
        return $this->removeAsterisks($titleText);
    }

    /**
     * Return configuration information inside square brackets from title content
     * Example: [config] title text
     *
     * @param $title
     * @return string
     */
    public function getTitleConfig($title): string
    {
        preg_match_all("/\\[(.*?)\\]/", $title, $matches);
        return $matches[1][0] ?? '';
    }

    /**
     * Returns title text if title content contains configuration information
     * Example: [config] title text
     *
     * @param $title
     * @return string
     */
    private function getTitleText($title): string
    {
        $parts = explode(']', $title);
        return $parts[1] ?? $parts[0];
    }

    /**
     * @param string $text
     * @return string
     */
    private function convertAsteriskToBoldTag(string $text): string
    {
        return preg_replace('#\*{2}(.*?)\*{2}#', '<b>$1</b>', $text);
    }

    /**
     * @param string $text
     * @return string
     */
    private function removeAsterisks(string $text): string
    {
        return str_replace('**', '', $text);
    }
}
