<div class="fieldset-wrapper" id="add-products-to-category-wrapper">
    <fieldset class="fieldset" id="add-products-to-category-wrapper-group-fields">
        <legend class="legend">
            <span><?=  __('Add Product To Categories') ?></span>
        </legend>
        <br>
        <div class="store-scope">
            <div class="store-tree" id="add-products-to-category-content">
                <?= $block->getChildHtml('tab_categories_add'); ?>
            </div>
        </div>
    </fieldset>
</div>
<div class="fieldset-wrapper" id="remove-products-from-category-wrapper">
    <fieldset class="fieldset" id="remove-products-from-category-wrapper-group-fields">
        <legend class="legend">
            <span><?=  __('Remove Product From Categories') ?></span>
        </legend>
        <br>
        <div class="store-scope">
            <div class="store-tree" id="remove-products-from-category-content">
                <?= $block->getChildHtml('tab_categories_remove'); ?>
            </div>
        </div>
    </fieldset>
</div>
<style>
    #add-products-to-category-wrapper #add-products-to-category-content ul ul,
    #remove-products-from-category-wrapper #remove-products-from-category-content ul ul
    {
        padding-left: 20px;
        border-left: 1px #cccccc dashed;
    }
    #add-products-to-category-wrapper #add-products-to-category-content ul li,
    #remove-products-from-category-wrapper #remove-products-from-category-content ul li
    {
        list-style: none;
    }
</style>