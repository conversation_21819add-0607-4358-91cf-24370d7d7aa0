<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Block\Catalog\Product\View;

use Magento\Catalog\Model\Product;
use Magento\Framework\View\Element\Template;
use Magento\Payment\Model\Checks\TotalMinMax;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\UrlInterface;
use Magento\Framework\RequireJs\Config as RequireJsConfig;
use \Payolution\Payments\Helper\Data;

class Instalment extends \Magento\Framework\View\Element\Template
{
    /**
     * @var Data
     */
    protected $_payolutionHelper;

    /**
     * @var Product
     */
    protected $_product;

    /**
     * @var \Magento\Framework\Locale\FormatInterface
     */
    protected $_localeFormat;

    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Payolution\Payments\Model\Instalment
     */
    protected $_instalmentPaymentMethod;

    /**
     * @var \Magento\Framework\View\Asset\Repository
     */
    protected $_assetRepo;

    /**
     * @var \Magento\Framework\Pricing\PriceCurrencyInterface
     */
    protected $_priceCurrency;

    /**
     * @param \Magento\Framework\Locale\FormatInterface $localeFormat
     * @param Template\Context $context
     * @param Data $payolutionHelper
     * @param \Magento\Framework\Registry $registry
     * @param \Payolution\Payments\Model\Instalment $instalmentPayment
     * @param \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Locale\FormatInterface $localeFormat,
        Template\Context $context,
        Data $payolutionHelper,
        \Magento\Framework\Registry $registry,
        \Payolution\Payments\Model\Instalment $instalmentPayment,
        \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency,
        array $data = []
    ) {
        $this->_localeFormat            = $localeFormat;
        $this->_assetRepo               = $context->getAssetRepository();
        $this->_payolutionHelper        = $payolutionHelper;
        $this->_coreRegistry            = $registry;
        $this->_instalmentPaymentMethod = $instalmentPayment;
        $this->_priceCurrency           = $priceCurrency;
        parent::__construct($context, $data);
    }

    /**
     * Insert instalment-calculator.js in <head/>
     *
     * @return $this
     */
    protected function _prepareLayout()
    {
        $assetCollection = $this->pageConfig->getAssetCollection();
        $after = RequireJsConfig::REQUIRE_JS_FILE_NAME;
        $relPath = $this->_payolutionHelper->getInstalmentJsDir()
            . \Payolution\Payments\Helper\Data::MEDIA_PAYOLUTION_JS_FILE_NAME;
        $instalmentCalculatorConfig = $this->_assetRepo->createArbitrary(
            $relPath,
            '',
            DirectoryList::MEDIA,
            UrlInterface::URL_TYPE_MEDIA
        );
        $assetCollection->insert(
            $instalmentCalculatorConfig->getFilePath(),
            $instalmentCalculatorConfig,
            $after
        );
        return parent::_prepareLayout();
    }

    /**
     * @return Product
     */
    public function getProduct()
    {
        if (!$this->_product) {
            $this->_product = $this->_coreRegistry->registry('product');
        }
        return $this->_product;
    }

    /**
     * Render only if instalment is active and product value is above min order amount for instalment
     *
     * @return string
     */
    protected function _toHtml()
    {
        if (!$this->_isAvailableForCurrentProduct()) {
            return '';
        }
        return parent::_toHtml();
    }

    /**
     * Check if instalment is active and product value is above configured threshold
     *
     * @return bool
     */
    protected function _isAvailableForCurrentProduct()
    {
        if (!$this->_instalmentPaymentMethod->isActive()) {
            return false;
        }
        $minTotal = $this->_instalmentPaymentMethod->getConfigData(TotalMinMax::MIN_ORDER_TOTAL);
        if (!is_null($minTotal) && ($this->getInstalmentTotalAmount() > $minTotal)) {
            return true;
        }
        return false;
    }

    /**
     * Retrieve dialog link options
     *
     * @return string
     */
    public function getInstalmentDialogOptions()
    {
        return 'jQuery("#instalment_dialog").instalmentProductDialog({'
            . 'instalmentTotalAmount:' . $this->getInstalmentTotalAmount()
            . ', priceConfig: ' . $this->getPriceConfigJson()
            . ', isProductView: 1'
            . '}).instalmentProductDialog("showDialog");';
    }

    /**
     * Fetch config for price formatting
     *
     * @return string
     */
    public function getPriceConfigJson()
    {
        return json_encode($this->_localeFormat->getPriceFormat());
    }

    /**
     * Fetch amount the instalment plan will be calculated with
     *
     * @return float
     */
    public function getInstalmentTotalAmount()
    {
        $storeScope = $this->_storeManager->getStore()->getId();
        $currentCurrency = $this->_storeManager->getStore()->getCurrentCurrencyCode();

        try {
            // get price depending on store scope and currency
            $price = $this->_priceCurrency->convert($this->getProduct()->getFinalPrice(), $storeScope, $currentCurrency);
            return $price;
        } catch (\Exception $e) {
            $this->_logger->error($e);
        }

        // fallback on default price
        return $this->getProduct()->getFinalPrice();
    }
}