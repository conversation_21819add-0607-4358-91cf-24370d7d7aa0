<?php
return [
    'modules' => [
        'Magento_Store' => 1,
        'Magento_AdminNotification' => 0,
        'Magento_Directory' => 1,
        'Magento_Config' => 1,
        'Magento_AsyncConfig' => 1,
        'Magento_Theme' => 1,
        'Magento_Backend' => 1,
        'Magento_Variable' => 1,
        'Magento_Eav' => 1,
        'Magento_Authorization' => 1,
        'Magento_Backup' => 1,
        'Magento_Customer' => 1,
        'Magento_Indexer' => 1,
        'Magento_CacheInvalidate' => 0,
        'Magento_Cms' => 1,
        'Magento_Rule' => 1,
        'Magento_GraphQl' => 1,
        'Magento_EavGraphQl' => 1,
        'Magento_Search' => 1,
        'Magento_CatalogImportExport' => 1,
        'Magento_Catalog' => 1,
        'Magento_CatalogInventory' => 1,
        'Magento_CatalogRule' => 1,
        'Magento_Payment' => 1,
        'Magento_CatalogRuleGraphQl' => 1,
        'Magento_CatalogSearch' => 1,
        'Magento_CatalogUrlRewrite' => 1,
        'Magento_GraphQlResolverCache' => 1,
        'Magento_MediaStorage' => 1,
        'Magento_Quote' => 1,
        'Magento_SalesSequence' => 1,
        'Magento_CheckoutAgreementsGraphQl' => 1,
        'Magento_Robots' => 1,
        'Magento_CmsGraphQl' => 1,
        'Magento_CmsUrlRewrite' => 1,
        'Magento_CmsUrlRewriteGraphQl' => 1,
        'Magento_StoreGraphQl' => 1,
        'Magento_Security' => 1,
        'Magento_Msrp' => 1,
        'Magento_Sales' => 1,
        'Magento_Checkout' => 1,
        'Magento_Contact' => 1,
        'Magento_ContactGraphQl' => 1,
        'Magento_Cookie' => 1,
        'Magento_Cron' => 1,
        'Magento_Csp' => 1,
        'Magento_Widget' => 1,
        'Magento_Bundle' => 1,
        'Magento_Downloadable' => 1,
        'Magento_Newsletter' => 1,
        'Magento_DataExporter' => 1,
        'Magento_DataMigrationTool' => 1,
        'Magento_Deploy' => 1,
        'Magento_Developer' => 1,
        'Magento_AdvancedSearch' => 1,
        'Magento_DirectoryGraphQl' => 1,
        'Magento_CatalogGraphQl' => 1,
        'Magento_QuoteGraphQl' => 1,
        'Magento_CatalogCustomerGraphQl' => 1,
        'Magento_BundleGraphQl' => 1,
        'Magento_Elasticsearch' => 1,
        'Magento_Elasticsearch7' => 1,
        'Magento_Email' => 1,
        'Magento_User' => 1,
        'Magento_GiftMessage' => 1,
        'Magento_GiftMessageGraphQl' => 1,
        'Magento_GoogleAnalytics' => 1,
        'Magento_GoogleGtag' => 1,
        'Magento_GraphQlServer' => 1,
        'Magento_PageCache' => 1,
        'Magento_GraphQlNewRelic' => 1,
        'Magento_CatalogCmsGraphQl' => 1,
        'Magento_AdminGraphQlServer' => 1,
        'Magento_GroupedProduct' => 1,
        'Magento_GroupedCatalogInventory' => 1,
        'Magento_GroupedProductGraphQl' => 1,
        'Magento_ImportExport' => 1,
        'Magento_ConfigurableProduct' => 1,
        'Magento_InstantPurchase' => 1,
        'Magento_Integration' => 1,
        'Magento_IntegrationGraphQl' => 1,
        'Magento_Inventory' => 1,
        'Magento_InventoryAdminUi' => 0,
        'Magento_InventoryAdvancedCheckout' => 1,
        'Magento_InventoryApi' => 1,
        'Magento_InventoryBundleImportExport' => 1,
        'Magento_InventoryBundleProduct' => 0,
        'Magento_InventoryBundleProductAdminUi' => 0,
        'Magento_InventoryBundleProductIndexer' => 1,
        'Magento_InventoryCatalog' => 1,
        'Magento_InventorySales' => 1,
        'Magento_InventoryCatalogAdminUi' => 0,
        'Magento_InventoryCatalogApi' => 1,
        'Magento_InventoryCatalogFrontendUi' => 1,
        'Magento_InventoryCatalogSearch' => 0,
        'Magento_InventoryCatalogSearchBundleProduct' => 1,
        'Magento_InventoryCatalogSearchConfigurableProduct' => 1,
        'Magento_ConfigurableProductGraphQl' => 1,
        'Magento_InventoryConfigurableProduct' => 0,
        'Magento_InventoryConfigurableProductFrontendUi' => 1,
        'Magento_InventoryConfigurableProductIndexer' => 0,
        'Magento_InventoryConfiguration' => 1,
        'Magento_InventoryConfigurationApi' => 1,
        'Magento_InventoryDistanceBasedSourceSelection' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionAdminUi' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionApi' => 1,
        'Magento_InventoryElasticsearch' => 1,
        'Magento_InventoryExportStockApi' => 1,
        'Magento_InventoryIndexer' => 1,
        'Magento_InventorySalesApi' => 1,
        'Magento_InventoryGroupedProduct' => 0,
        'Magento_InventoryGroupedProductAdminUi' => 0,
        'Magento_InventoryGroupedProductIndexer' => 0,
        'Magento_InventoryImportExport' => 0,
        'Magento_InventoryInStorePickupApi' => 1,
        'Magento_Ui' => 1,
        'Magento_InventorySourceSelectionApi' => 0,
        'Magento_InventoryInStorePickup' => 1,
        'Magento_InventoryInStorePickupGraphQl' => 1,
        'Magento_Shipping' => 1,
        'Magento_InventoryInStorePickupShippingApi' => 1,
        'Magento_InventoryInStorePickupQuoteGraphQl' => 1,
        'Magento_InventoryInStorePickupSales' => 1,
        'Magento_InventoryInStorePickupSalesApi' => 1,
        'Magento_InventoryInStorePickupQuote' => 1,
        'Magento_InventoryInStorePickupShipping' => 1,
        'Magento_InventoryInStorePickupShippingAdminUi' => 1,
        'Magento_InventoryInStorePickupMultishipping' => 1,
        'Magento_Webapi' => 1,
        'Magento_InventoryCache' => 0,
        'Magento_InventoryLowQuantityNotification' => 0,
        'Magento_Reports' => 1,
        'Magento_InventoryLowQuantityNotificationApi' => 0,
        'Magento_InventoryMultiDimensionalIndexerApi' => 1,
        'Magento_InventoryProductAlert' => 0,
        'Magento_InventoryQuoteGraphQl' => 1,
        'Magento_InventoryRequisitionList' => 1,
        'Magento_InventoryReservations' => 1,
        'Magento_InventoryReservationCli' => 1,
        'Magento_InventoryReservationsApi' => 1,
        'Magento_InventoryExportStock' => 1,
        'Magento_InventorySalesAdminUi' => 0,
        'Magento_CatalogInventoryGraphQl' => 1,
        'Magento_InventorySalesAsyncOrder' => 1,
        'Magento_InventorySalesFrontendUi' => 0,
        'Magento_InventorySetupFixtureGenerator' => 1,
        'Magento_InventoryShipping' => 1,
        'Magento_InventoryShippingAdminUi' => 1,
        'Magento_InventorySourceDeductionApi' => 1,
        'Magento_InventorySourceSelection' => 1,
        'Magento_InventoryInStorePickupFrontend' => 1,
        'Magento_InventorySwatchesFrontendUi' => 1,
        'Magento_InventoryVisualMerchandiser' => 1,
        'Magento_InventoryWishlist' => 1,
        'Magento_JwtFrameworkAdapter' => 1,
        'Magento_JwtUserToken' => 1,
        'Magento_LayeredNavigation' => 1,
        'Magento_LoginAsCustomer' => 1,
        'Magento_LoginAsCustomerAdminUi' => 1,
        'Magento_LoginAsCustomerApi' => 1,
        'Magento_LoginAsCustomerAssistance' => 1,
        'Magento_LoginAsCustomerFrontendUi' => 1,
        'Magento_LoginAsCustomerGraphQl' => 1,
        'Magento_LoginAsCustomerLog' => 1,
        'Magento_LoginAsCustomerPageCache' => 1,
        'Magento_LoginAsCustomerQuote' => 1,
        'Magento_LoginAsCustomerSales' => 1,
        'Magento_MediaContent' => 1,
        'Magento_MediaContentApi' => 1,
        'Magento_MediaContentCatalog' => 1,
        'Magento_MediaContentCms' => 1,
        'Magento_MediaContentSynchronization' => 1,
        'Magento_MediaContentSynchronizationApi' => 1,
        'Magento_MediaContentSynchronizationCatalog' => 1,
        'Magento_MediaContentSynchronizationCms' => 1,
        'Magento_MediaGallery' => 1,
        'Magento_MediaGalleryApi' => 1,
        'Magento_MediaGalleryCatalog' => 1,
        'Magento_MediaGalleryCatalogIntegration' => 1,
        'Magento_MediaGalleryCatalogUi' => 1,
        'Magento_MediaGalleryCmsUi' => 1,
        'Magento_MediaGalleryIntegration' => 1,
        'Magento_MediaGalleryMetadata' => 1,
        'Magento_MediaGalleryMetadataApi' => 1,
        'Magento_MediaGalleryRenditions' => 1,
        'Magento_MediaGalleryRenditionsApi' => 1,
        'Magento_MediaGallerySynchronization' => 1,
        'Magento_MediaGallerySynchronizationApi' => 1,
        'Magento_MediaGallerySynchronizationMetadata' => 1,
        'Magento_MediaGalleryUi' => 1,
        'Magento_MediaGalleryUiApi' => 1,
        'Magento_CatalogWidget' => 1,
        'Magento_MessageQueue' => 1,
        'Magento_InventoryConfigurableProductAdminUi' => 0,
        'Magento_MsrpConfigurableProduct' => 1,
        'Magento_MsrpGroupedProduct' => 1,
        'Magento_MysqlMq' => 1,
        'Magento_NewRelicReporting' => 1,
        'Magento_CustomerGraphQl' => 1,
        'Magento_NewsletterGraphQl' => 1,
        'Magento_OfflinePayments' => 1,
        'Magento_SalesRule' => 1,
        'Magento_OpenSearch' => 1,
        'Magento_OrderCancellation' => 1,
        'Magento_OrderCancellationGraphQl' => 1,
        'Magento_OrderCancellationUi' => 1,
        'Magento_Sitemap' => 1,
        'Magento_GraphQlCache' => 1,
        'Magento_Captcha' => 1,
        'Magento_PaymentGraphQl' => 1,
        'Magento_ServiceProxy' => 1,
        'Magento_Vault' => 1,
        'Magento_PaymentServicesDashboard' => 1,
        'Magento_PaymentServicesPaypalGraphQl' => 1,
        'Magento_QueryXml' => 1,
        'Magento_Paypal' => 1,
        'Magento_PaypalGraphQl' => 1,
        'Magento_Persistent' => 1,
        'Magento_ProductAlert' => 1,
        'Magento_ProductVideo' => 1,
        'Magento_ServicesConnector' => 1,
        'Magento_CheckoutAgreements' => 1,
        'Magento_QuoteBundleOptions' => 1,
        'Magento_QuoteConfigurableOptions' => 1,
        'Magento_QuoteDownloadableLinks' => 1,
        'Magento_DownloadableGraphQl' => 1,
        'Magento_ReCaptchaAdminUi' => 1,
        'Magento_ReCaptchaCheckout' => 1,
        'Magento_ReCaptchaCheckoutSalesRule' => 1,
        'Magento_ReCaptchaContact' => 1,
        'Magento_ReCaptchaCustomer' => 1,
        'Magento_ReCaptchaFrontendUi' => 1,
        'Magento_ReCaptchaMigration' => 1,
        'Magento_ReCaptchaNewsletter' => 1,
        'Magento_ReCaptchaPaypal' => 1,
        'Magento_ReCaptchaReview' => 1,
        'Magento_ReCaptchaStorePickup' => 1,
        'Magento_ReCaptchaUi' => 1,
        'Magento_ReCaptchaUser' => 1,
        'Magento_ReCaptchaValidation' => 1,
        'Magento_ReCaptchaValidationApi' => 1,
        'Magento_ReCaptchaVersion2Checkbox' => 1,
        'Magento_ReCaptchaVersion2Invisible' => 1,
        'Magento_ReCaptchaVersion3Invisible' => 1,
        'Magento_ReCaptchaWebapiApi' => 1,
        'Magento_ReCaptchaWebapiGraphQl' => 1,
        'Magento_ReCaptchaWebapiRest' => 1,
        'Magento_ReCaptchaWebapiUi' => 1,
        'Magento_ReCaptchaWishlist' => 1,
        'Magento_RelatedProductGraphQl' => 1,
        'Magento_ReleaseNotification' => 1,
        'Magento_RemoteStorage' => 1,
        'Magento_InventoryLowQuantityNotificationAdminUi' => 0,
        'Magento_RequireJs' => 1,
        'Magento_Review' => 1,
        'Magento_ReviewGraphQl' => 1,
        'Magento_PageBuilder' => 1,
        'Magento_Rss' => 1,
        'Magento_AwsS3' => 1,
        'Magento_ConfigurableProductSales' => 1,
        'Magento_ServicesId' => 1,
        'Magento_SalesGraphQl' => 1,
        'Magento_SalesInventory' => 1,
        'Magento_OfflineShipping' => 1,
        'Magento_SalesRuleGraphQl' => 1,
        'Magento_CatalogRuleConfigurable' => 1,
        'Magento_UrlRewriteGraphQl' => 1,
        'Magento_Analytics' => 1,
        'Magento_Securitytxt' => 1,
        'Magento_ServicesIdGraphQlServer' => 1,
        'Magento_ServicesIdLayout' => 1,
        'Magento_PaymentServicesBase' => 1,
        'Magento_SalesDataExporter' => 1,
        'Magento_PaymentServicesPaypal' => 1,
        'Magento_InventoryInStorePickupSalesAdminUi' => 1,
        'Magento_AwsS3PageBuilder' => 1,
        'Magento_AsynchronousOperations' => 1,
        'Magento_StoreDataExporter' => 1,
        'Magento_CompareListGraphQl' => 1,
        'Magento_Swatches' => 1,
        'Magento_SwatchesGraphQl' => 1,
        'Magento_SwatchesLayeredNavigation' => 1,
        'Magento_Tax' => 1,
        'Magento_TaxGraphQl' => 1,
        'Magento_CustomerDownloadableGraphQl' => 1,
        'Magento_ThemeGraphQl' => 1,
        'Magento_Translation' => 1,
        'Magento_InventoryInStorePickupAdminUi' => 1,
        'Magento_UrlRewrite' => 1,
        'Magento_CatalogUrlRewriteGraphQl' => 1,
        'Magento_EncryptionKey' => 1,
        'Magento_InventoryGraphQl' => 1,
        'Magento_PaypalCaptcha' => 1,
        'Magento_VaultGraphQl' => 1,
        'Magento_InventoryInStorePickupWebapiExtension' => 1,
        'Magento_WebapiAsync' => 0,
        'Magento_WebapiSecurity' => 1,
        'Magento_Weee' => 1,
        'Magento_WeeeGraphQl' => 1,
        'Magento_CurrencySymbol' => 1,
        'Magento_Wishlist' => 1,
        'Magento_WishlistGraphQl' => 1,
        'Amasty_Base' => 1,
        'Amasty_Conditions' => 1,
        'Amasty_CommonRules' => 1,
        'Amasty_Payrestriction' => 1,
        'AvS_ScopeHint' => 1,
        'CleverReach_CleverReachIntegration' => 1,
        'CopeX_AutoRedirectHyva' => 1,
        'CopeX_CleanCustomers' => 1,
        'CopeX_Cleanup' => 1,
        'CopeX_CookieNotification' => 1,
        'CopeX_CopyrightHtml' => 1,
        'CopeX_Core' => 1,
        'CopeX_DataMigration' => 1,
        'CopeX_DeleteMe' => 1,
        'Hyva_Theme' => 1,
        'CopeX_PRG' => 1,
        'EthanYehuda_CronjobManager' => 1,
        'Experius_EmailCatcher' => 1,
        'Experius_WysiwygDownloads' => 1,
        'FireGento_MageSetup' => 1,
        'Fooman_Totals' => 1,
        'Fooman_Surcharge' => 1,
        'Fooman_SurchargePayment' => 1,
        'Hyva_CmsTailwindJit' => 1,
        'Hyva_CompatModuleFallback' => 1,
        'Hyva_Email' => 1,
        'Hyva_GraphqlTokens' => 1,
        'Hyva_GraphqlViewModel' => 1,
        'Hyva_ThemeFallback' => 1,
        'Hyva_MollieThemeBundle' => 1,
        'Hyva_OrderCancellationWebapi' => 1,
        'Hyva_PaymentIcons' => 1,
        'CopeX_HyvaTheme' => 1,
        'Hyva_LumaCheckout' => 1,
        'Hyva_Widgets' => 1,
        'MSP_Common' => 1,
        'MSP_DevTools' => 1,
        'MagePal_Core' => 1,
        'MagePal_CustomerAccountLinksManager' => 1,
        'MagePal_PreviewCheckoutSuccessPage' => 1,
        'Mageplaza_Core' => 1,
        'Mageplaza_GoogleTagManager' => 1,
        'Mollie_Payment' => 1,
        'Mollie_HyvaCompatibility' => 1,
        'OlegKoval_RegenerateUrlRewrites' => 1,
        'Opengento_Gdpr' => 1,
        'Xtento_XtCore' => 1,
        'Xtento_ProductExport' => 1,
        'Yireo_CspUtilities' => 1,
        'Yireo_GoogleTagManager2' => 1,
        'Yireo_Whoops' => 1
    ]
];
