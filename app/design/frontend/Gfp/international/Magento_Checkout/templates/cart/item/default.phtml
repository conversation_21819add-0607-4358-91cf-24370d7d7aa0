<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var $block \Magento\Checkout\Block\Cart\Item\Renderer */

$_item = $block->getItem();
$product = $_item->getProduct();

$quoteItemCssClass = "";

$isVisibleProduct = $product->isVisibleInSiteVisibility();
/** @var \Magento\Msrp\Helper\Data $helper */
$helper = $this->helper('Magento\Msrp\Helper\Data');

$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$priceHelper = $objectManager->create('Magento\Framework\Pricing\Helper\Data');

$canApplyMsrp = $helper->isShowBeforeOrderConfirm($product) && $helper->isMinimalPriceLessMsrp($product);

?>
<tbody class="cart item<?php echo $quoteItemCssClass?>">
    <tr class="item-info">
        <td data-th="<?php echo $block->escapeHtml(__('Item')); ?>" class="col item">
            <?php if ($block->hasProductUrl()):?>
                <a href="<?php /* @escapeNotVerified */ echo $block->getProductUrl() ?>"
                   title="<?php echo $block->escapeHtml($block->getProductName()) ?>"
                   tabindex="-1"
                   class="product-item-photo">
            <?php else:?>
                <span class="product-item-photo">
            <?php endif;?>
            <?php echo $block->getImage($block->getProductForThumbnail(), 'cart_page_product_thumbnail')->toHtml(); ?>
            <?php if ($block->hasProductUrl()):?>
                </a>
            <?php else: ?>
                </span>
            <?php endif; ?>
            <div class="product-item-details">
                <strong class="product-item-name">
                    <?php if ($block->hasProductUrl()):?>
                        <a href="<?php /* @escapeNotVerified */ echo $block->getProductUrl() ?>"><?php echo $block->escapeHtml($block->getProductName()) ?></a>
                    <?php else: ?>
                        <?php echo $block->escapeHtml($block->getProductName()) ?>
                    <?php endif; ?>
                </strong>
                <?php if ($_options = $block->getOptionList()):?>
                    <dl class="item-options">
                        <?php foreach ($_options as $_option) : ?>
                            <?php $_formatedOptionValue = $block->getFormatedOptionValue($_option) ?>
                            <dd>
                                <?php if (isset($_formatedOptionValue['full_view'])): ?>
                                    <?php /* @escapeNotVerified */ echo $_formatedOptionValue['full_view'] ?>
                                <?php else: ?>
                                    <?php /* @escapeNotVerified */ echo $_formatedOptionValue['value'] ?>
                                <?php endif; ?>
                            </dd>
                        <?php endforeach; ?>
                        <?php if($_item->getProduct()->getData('length')): ?>
                            <dd>
                                <?php echo $_item->getProduct()->getResource()->getAttribute('length')->getFrontendLabel(); ?>:
                                <?php echo $_item->getProduct()->getData('length'); ?>
                            </dd>
                        <?php endif; ?>
                    </dl>
                <?php endif;?>
                <?php if ($messages = $block->getMessages()): ?>
                    <?php foreach ($messages as $message): ?>
                        <div class="cart item message <?php /* @escapeNotVerified */ echo $message['type'] ?>"><div><?php echo $block->escapeHtml($message['text']) ?></div></div>
                    <?php endforeach; ?>
                <?php endif; ?>
                <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
                <?php if ($addInfoBlock): ?>
                    <?php echo $addInfoBlock->setItem($_item)->toHtml() ?>
                <?php endif;?>
            </div>
        </td>

        <?php if ($canApplyMsrp): ?>
            <td class="col msrp" data-th="<?php echo $block->escapeHtml(__('Price')); ?>">
                <span class="pricing msrp">
                    <span class="msrp notice"><?php /* @escapeNotVerified */ echo __('See price before order confirmation.'); ?></span>
                    <?php $helpLinkId = 'cart-msrp-help-' . $_item->getId(); ?>
                    <a href="#" class="action help map" id="<?php /* @escapeNotVerified */ echo($helpLinkId); ?>" data-mage-init='{"addToCart":{"helpLinkId": "#<?php /* @escapeNotVerified */ echo $helpLinkId;?>","productName": "<?php /* @escapeNotVerified */ echo $product->getName(); ?>","showAddToCart": false}}'>
                        <span><?php /* @escapeNotVerified */ echo __("What's this?"); ?></span>
                    </a>
                </span>
            </td>
        <?php else: ?>
            <td class="col price" data-th="<?php echo $block->escapeHtml(__('Price')); ?>">
                <?= $block->getUnitPriceHtml($_item); ?>
            </td>
        <?php endif; ?>

        <td class="col qty" data-th="<?= $block->escapeHtml(__('Qty')); ?>">
            <div class="field qty">
                <label class="label" for="cart-<?php /* @escapeNotVerified */
                echo $_item->getId() ?>-qty">
                    <span><?php /* @escapeNotVerified */
                        echo __('Qty') ?></span>
                </label>
                <div class="control qty">
                    <input id="cart-<?php /* @escapeNotVerified */
                    echo $_item->getId() ?>-qty"
                           name="cart[<?php /* @escapeNotVerified */
                           echo $_item->getId() ?>][qty]"
                           data-cart-item-id="<?php /* @escapeNotVerified */
                           echo $_item->getSku() ?>"
                           value="<?php /* @escapeNotVerified */
                           echo $block->getQty() ?>"
                           type="number"
                           size="4"
                           title="<?php echo $block->escapeHtml(__('Qty')); ?>"
                           class="input-text qty"
                           maxlength="12"
                           data-validate="{required:true,'validate-greater-than-zero':true}"
                           data-role="cart-item-qty"/>
                </div>
            </div>
        </td>

        <td class="col item-actions" data-th="<?php echo $block->escapeHtml(__('Aktion'));?>">
          <div class="actions-toolbar">
          <?php /* @escapeNotVerified */ echo $block->getActions($_item) ?>
          </div>
        </td>
    </tr>

</tbody>
