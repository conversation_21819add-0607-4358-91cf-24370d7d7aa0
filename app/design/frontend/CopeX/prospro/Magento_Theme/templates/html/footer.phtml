<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\ViewModel\HyvaCsp;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Cms\Block\Block;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var HyvaCsp $hyvaCsp */

// Remove any child blocks that don't need to be part of the footer columns
$footerColumns = array_diff(
    $block->getChildNames(),
    array('footer-copyright', 'form.subscribe', 'footer-content-before', 'footer-content-after')
);
?>

<?= $block->getChildHtml('footer-content-before') ?>
<?= $block->getChildHtml('subscribe') ?>
<div class="bg-zinc-100 border-t border-zinc-200">
    <div class="container py-8 mx-auto">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 md:gap-6 text-slate-600">
            <div class="mt-8 lg:mt-0">
                <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('block_footer_column1')->toHtml(); ?>
            </div>
            <div class="mt-8 lg:mt-0">
                <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('block_footer_column2')->toHtml(); ?>
            </div>
            <div class="mt-8 lg:mt-0">
                <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('block_footer_column3')->toHtml(); ?>
            </div>
            <div class="mt-8 lg:mt-0">
                <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('block_footer_column4')->toHtml(); ?>
            </div>
            <div class="mt-8 lg:mt-0">
                <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('block_footer_column5')->toHtml(); ?>
            </div>
            <div class="mt-8 lg:mt-0">
                <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('block_footer_column6')->toHtml(); ?>
            </div>
        </div>
    </div>
    <?php if ($footerContentAfter = $block->getChildHtml('footer-content-after')): ?>
        <div class="space-y-10 bg-zinc-50 py-4">
            <?= /** @noEscape */ $footerContentAfter ?>
        </div>
    <?php endif; ?>
    <div class="container pt-7 pb-5 border-t border-zinc-200 block lg:flex justify-start flex-col lg:flex-row text-sm">
        <div class="text-center lg:text-left">
            <?php echo $this->getLayout()->createBlock(Template::class)->setTemplate('Magento_Theme::html/footer/store_logo.phtml')->toHtml(); ?>
        </div>
        <div class="flex justify-center lg:justify-start">
            <div class="mt-4 lg:mt-0">
                <div class="px-0 md:px-4 mx-4">
                    <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('footer_contactinfo')->toHtml(); ?>
                </div>
            </div>
            <div class="mt-4 lg:mt-0 mx-0 md:mx-4 mx-4">
                <div class="px-0 md:px-4">
                    <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('footer_infolinks')->toHtml(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function mobileFooterCollpase() {
        return {
            open: false,
            isMobile: false,
            init() {
                this.open = this.$root.dataset.open;
                this.checkIsMobileResolution();
            },
            checkIsMobileResolution() {
                this.isMobile = getComputedStyle(this.$refs.toggleIcon).display !== 'none'
            },
            isOpen() {
                return this.isMobile ? this.open : true;
            },
            toggle() {
                return this.isMobile ? this.open = !this.open : true;
            },
            eventListeners: {
                ['@resize.window.debounce']() {
                    this.checkIsMobileResolution();
                },
                ['@visibilitychange.window.debounce']() {
                    this.checkIsMobileResolution();
                }
            },
            setToggleRoles: {
                [':role']() {
                    return this.isMobile && 'button';
                },
                [':tabindex']() {
                    return this.isMobile && '0';
                },
                [':aria-expanded']() {
                    return this.isMobile ? this.open : null;
                },
                [':aria-controls']() {
                    return this.isMobile ? this.$el.dataset.id : null;
                }
            },
            setCollapseRoles: {
                [':aria-hidden']() {
                    return this.isMobile ? !!this.open : null;
                },
                [':aria-labelledby']() {
                    return this.isMobile ? this.$el.id : null;
                }
            }
        }
    }
    window.addEventListener('alpine:init', () => Alpine.data('mobileFooterCollpase', mobileFooterCollpase), { once: true })
</script>
<?php $hyvaCsp->registerInlineScript() ?>

