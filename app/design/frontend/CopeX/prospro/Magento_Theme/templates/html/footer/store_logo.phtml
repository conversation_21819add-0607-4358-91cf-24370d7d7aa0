<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use CopeX\HyvaTheme\ViewModel\Footer;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);

/** @var Footer $logo */
$footer = $viewModels->require(Footer::class);
$storeName = $footer->getStoreLogoAlt();
$logoSrc = $footer->getStoreLogoUrl();
$homeUrl = $footer->getHomeUrl();
$logoWidth = 160;
$logoHeight = 120;
?>
<div class="pr-0 md:pr-4">
    <a
        class="inline-block align-middle"
        href="<?= $escaper->escapeUrl($homeUrl) ?>"
        title="<?= $escaper->escapeHtmlAttr(__('Go to Home page')) ?>"
    >
        <?php if ($logoSrc): ?>
            <img
                src="<?= $escaper->escapeUrl($logoSrc) ?>"
                alt="<?= $escaper->escapeHtmlAttr($storeName) ?>"
                width="<?= $escaper->escapeHtmlAttr($logoWidth) ?>"
                height="<?= $escaper->escapeHtmlAttr($logoHeight) ?>"
                loading="eager"
                fetchpriority="high"
            >
        <?php else: ?>
            <span class="text-xl font-medium font-title">
            <?= $escaper->escapeHtml($storeName) ?>
        </span>
        <?php endif; ?>
    </a>
</div>
