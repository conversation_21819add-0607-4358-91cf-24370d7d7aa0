<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\PaymentIcons\ViewModel\PaymentIconsLight;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Cms\Block\Block;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var PaymentIconsLight $paymentIcons */
$paymentIcons = $viewModels->require(PaymentIconsLight::class);

$paymentIconsWidth = 48;
$paymentIconsHeight = 32;
?>

<div class="container block lg:flex w-full justify-between">
    <div class="flex gap-4 flex-wrap" aria-label="<?= $escaper->escapeHtml(__('Payment Providers')) ?>">
        <?= $paymentIcons->idealHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->visaHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->mastercardHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->paypalHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->klarnaHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->applePayHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->googlePayHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->bitcoinHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
    </div>
    <div class="flex gap-4 flex-wrap">
        <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('footer_barlinks')->toHtml(); ?>
    </div>
</div>

<div class="container flex w-full justify-start mt-4">
    <span style="line-height:40px;"><?= $escaper->escapeHtml(__('Social Media')) ?>:</span>
    <a href="#">
        <img class="mx-2" src="<?= $block->getViewFileUrl('images/facebook.png') ?>" alt="Facebook" style="width:auto;height:40px;"/>
    </a>
    <a href="mailto:<EMAIL>">
        <img class="mx-2" src="<?= $block->getViewFileUrl('images/mail.png') ?>" alt="Mail" style="width:auto;height:40px;"/>
    </a>
</div>
