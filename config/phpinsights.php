<?php

declare(strict_types=1);

return [
    'preset' => 'magento2',
    'ide' => 'phpstorm',
    'exclude' => [
        'config',
        'app/etc/env.php'
    ],
    'add' => [
        //  ExampleMetric::class => [
        //      ExampleInsight::class,
        //  ]
    ],
    'remove' => [
        \NunoMaduro\PhpInsights\Domain\Sniffs\ForbiddenSetterSniff::class,
        \SlevomatCodingStandard\Sniffs\TypeHints\DeclareStrictTypesSniff::class,
    ],
    'config' => [
        \PHP_CodeSniffer\Standards\Generic\Sniffs\Files\LineLengthSniff::class => [
            'lineLimit' => 120,
            'absoluteLineLimit' => 160,
        ]
    ],
    'requirements' => [
        'min-quality' => 89,
        'min-complexity' => 75,
        'min-architecture' => 75,
        'min-style' => 95,
        'disable-security-check' => true
    ],
    'threads' => null
];
