<?php
return [
    'modules'         =>
        [
            'ADM_QuickDevBar'                    => 0,
            'Yireo_Whoops'                       => 0,
            'MagePal_PreviewCheckoutSuccessPage' => 0,
            'Smile_DebugToolbar'                 => 0,
            'MSP_Common'                         => 0,
            'MSP_DevTools'                       => 0,
            'Salecto_MediaStorageSync'           => 0,
        ],
    'backend'         =>
        [
            'frontName' => 'backoffice',
        ],
    'install'         =>
        [
            'date' => 'Tue, 3 Feb 2019 08:43:05 +0000',
        ],
    'crypt'           =>
        [
            'key' => '84e9d819d0e98652d3e795c6db22f980',
        ],
    //    'session'         =>
    //        [
    //            'save' => 'files',
    //        ],
    'session'         =>
        [
            'save'  => 'redis',
            'redis' =>
                [
                    'host'                  => 'localhost',
                    'port'                  => '6379',
                    'password'              => '',
                    'timeout'               => '2.5',
                    'persistent_identifier' => '',
                    'database'              => '2',
                    'compression_threshold' => '2048',
                    'compression_library'   => 'gzip',
                    'log_level'             => '1',
                    'max_concurrency'       => '20',
                    'break_after_frontend'  => '5',
                    'break_after_adminhtml' => '30',
                    'first_lifetime'        => '600',
                    'bot_first_lifetime'    => '60',
                    'bot_lifetime'          => '7200',
                    'disable_locking'       => '1',
                    'min_lifetime'          => '60',
                    'max_lifetime'          => '2592000',
                ],
        ],
    'cache'           =>
        [
            'frontend' =>
                [
                    'default'    =>
                        [
                            'backend'         => 'Cm_Cache_Backend_Redis',
                            'backend_options' =>
                                [
                                    'server' => 'localhost',
                                    'port'   => '6379',
                                ],
                        ],
                    'page_cache' =>
                        [
                            'backend'         => 'Cm_Cache_Backend_Redis',
                            'backend_options' =>
                                [
                                    'server'        => 'localhost',
                                    'port'          => '6379',
                                    'database'      => '1',
                                    'compress_data' => '0',
                                ],
                        ],
                ],
        ],
    'db'              =>
        [
            'table_prefix' => '',
            'connection'   =>
                [
                    'default' =>
                        [
                            'host'     => 'localhost',
                            'dbname'   => 'prospro_production',
                            'username' => 'prospro_production',
                            'password' => '5a;NkF#L$?Rb',
                            'active'   => '1',
                        ],
                ],
        ],
    'resource'        =>
        [
            'default_setup' =>
                [
                    'connection' => 'default',
                ],
        ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE'       => 'production',
    'cache_types'     =>
        [
            'config'                 => 1,
            'layout'                 => 1,
            'block_html'             => 1,
            'collections'            => 1,
            'reflection'             => 1,
            'db_ddl'                 => 1,
            'eav'                    => 1,
            'config_integration'     => 1,
            'config_integration_api' => 1,
            'full_page'              => 1,
            'translate'              => 1,
            'config_webservice'      => 1,
        ],
    'directories'     =>
        [
            'document_root_is_pub' => true,
        ],
    'system'          => [
        'default' => [
            'web' =>[
                'secure' => [
                    'base_url' => 'https://staging.pros-pro.com/',
                    'base_link_url' => 'https://staging.pros-pro.com/'
                ],
                'unsecure' => [
                    'base_url' => 'https://staging.pros-pro.com/',
                    'base_link_url' => 'https://staging.pros-pro.com/'
                ]
            ],
            'smile_elasticsuite_core_base_settings' => [
                'es_client' => [
                    'servers' => 'https://os-pros-pro.rackspeed-cloud.de:9200',
                ],
            ],
            'tax'  => [
                'calculation' => [
                    'algorithm' => 'ROW_BASE_CALCULATION'
                ]
            ],
            'catalog'                               => [
                'search' => [
                    'engine' => "elasticsearch7",
                    "elasticsearch7_server_hostname" => "127.0.0.1",
                    'elasticsearch7_server_port' => "9200",
                    'elasticsearch'
                ],
            ],
            'xtcore'                                => [
                'adminnotification' => [
                    'enabled' => 0,
                ],
            ],
            'dev'                                   => [
                'debug'    => [
                    'debug_logging' => '0',
                ],
                'js'       => [
                    'merge_files'  => '1',
                    'minify_files' => '1',
                ],
                'css'      => [
                    'minify_files'    => '1',
                    'merge_css_files' => '1',
                ],
                'template' => [
                    'minify_html'   => '0',
                    'allow_symlink' => '1',
                ],
                'image' => [
                    'default_adapter' => 'IMAGEMAGICK'
                ],
                'static' => [
                    'sign' => 1
                ]
            ],
            'system' => [
                'backup' => [
                    'functionality_enabled' => 0
                ]
            ],
            'swissup_core' => [
                'notification'=> [
                    'enabled' => 0
                ]
            ],
            'emailcatcher' => [
                'general' => [
                    'enabled' => 1
                ]
            ]
        ]
    ],
    'queue' => [
        'consumers_wait_for_messages' => 1
    ],
    'http_cache_hosts' => [
        [
            'host' => 'varnish',
            'port' => '8080'
        ]
    ],
    'lock' => [
        'provider' => 'db',
        'config' => [
            'prefix' => null
        ]
    ]
];
