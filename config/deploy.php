<?php

namespace Deployer;

use Deployer\Task\Context;

require 'recipe/magento2.php';
//require __DIR__ . '/vendor/deployer/deployer/contrib/rsync.php';
const ENV_PRODUCTION = 'production';
const ENV_STAGING = 'staging';
/**
 * Settings
 */
set('docker',false);
set('application', 'pro-pro.com');
set('repository', '*******************:copex/pros-pro-m2.git');
set('deployment_root_path', '/home/<USER>/pros-pro.com');
set('keep_releases', 2);
set('php_version', "8.3");
set('hyva_theme',['CopeX/prospro']);
set('restart_command', '');
//set('writable_mode','skip');
// Array of shared files that will be added to the default shared_files without overriding
set('additional_shared_files', ['pub/.htaccess']);

// which languages should get deployed
set('static_content_locales', 'de_DE en_US');
// themes to deploy
set('magento_themes', array_merge(get('hyva_theme') ?? [],[ 'Magento/backend']));

set('bin/php', function(){
    $docker = get('docker');
    if ($docker) {
        $env = get('labels')['env'];
        if ($env == ENV_PRODUCTION) {
            return 'docker exec -u $(id -u ${USER}):www-data {{application}}-app php -d memory_limit=-1';
        }
        if ($env == ENV_STAGING) {
            return 'docker exec -u $(id -u ${USER}):www-data {{application}}-staging-app php -d memory_limit=-1';
        }
    }
    return "/usr/local/bin/php";
});

//deploy targets
include __DIR__ . '/deploy/production.php';
include __DIR__ . '/deploy/staging.php';
//custom tasks
include __DIR__ . '/deploy/tasks/dbTasks.php';
include __DIR__ . '/deploy/tasks/miscTasks.php';

// in case of rsync deployment this have to be enabled
//task('deploy:update_code')->disable();
//after('deploy:update_code', 'rsync');

//set php executable to docker container

//composer command
set('bin/composer', function () {
    $docker = get('docker');
    if ($docker) {
        return 'docker run --rm  -w="{{release_or_current_path}}" -v {{deployment_root_path}}:{{deployment_root_path}} -v /var/.composer:/.composer -u $(id -u ${USER}):www-data -e COMPOSER_MEMORY_LIMIT=-1 copex/php:{{php_version}} composer';
    }
    return '/opt/cpanel/composer/bin/composer';
});

//define n98-magerun command
set('bin/n98-magerun2', function () {
    $docker = get('docker');
    if ($docker) {
        return 'docker run --rm  -w="{{release_or_current_path}}" -v {{deployment_root_path}}:{{deployment_root_path}} -u $(id -u ${USER}):www-data -e COMPOSER_MEMORY_LIMIT=-1 copex/php:{{php_version}} ./bin/n98-magerun2';
    }
    return 'bin/n98-magerun2';
});


//copy env.php file
task('copy_config', function () {
    $env = get('labels')['env'];
    if ($env == ENV_PRODUCTION) {
        upload('config/etc/env.prod.php', '{{release_or_current_path}}/app/etc/env.php');
    }
    if ($env == ENV_STAGING) {
        upload('config/etc/env.staging.php', '{{release_or_current_path}}/app/etc/env.php');
    }
});


task('build_hyva', function(){
    $hyvaThemes = get('labels')['hyvaTheme'] ?? get('hyva_theme') ?? false;
    if($hyvaThemes){
        $env = get('labels')['env'];
        if(get('docker')){
            foreach ($hyvaThemes as $theme) {
                run('docker run --rm -u $(id -u ${USER}):www-data -w="{{release_or_current_path}}/app/design/frontend/' . $theme .
                    '/web/tailwind" -v {{deployment_root_path}}/{{application}}/' . $env . ':{{deployment_root_path}}/{{application}}/' . $env .
                    ' node:14-alpine npm ci');

                run('docker run --rm -u $(id -u ${USER}):www-data -w="{{release_or_current_path}}/app/design/frontend/' . $theme .
                    '/web/tailwind" -v {{deployment_root_path}}/{{application}}/' . $env . ':{{deployment_root_path}}/{{application}}/' . $env .
                    ' node:14-alpine npm run build-prod');
            }
        }else {
            foreach ($hyvaThemes as $theme) {
                run('npm --prefix {{release_or_current_path}}/app/design/frontend/' . $theme . '/web/tailwind ci');
                run('npm --prefix {{release_or_current_path}}/app/design/frontend/' . $theme . '/web/tailwind run build-prod');
            }
        }
    }
});


task('restart_webserver', function () {
    if(get('restart_command')){
        run ('{{restart_command}}');
    } else if(get('docker')) {
        run('docker restart {{application}}-app');
    }
});


after('deploy:failed', 'deploy:unlock');
before('deploy:shared', 'deploy:additional-shared');
before('magento:compile', 'copy_config');
before('magento:deploy:assets', 'build_hyva');
after('deploy:symlink', 'restart_webserver');
after("deploy:failed", 'deploy:unlock');
