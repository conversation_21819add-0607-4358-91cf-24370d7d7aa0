<?php

namespace Deployer;

/**
 * Hosts
 */
host('production')
    // If you need to hop through a SSH-Config Proxy
    ->setHostname(getenv('CI') === 'true' ? '***********' : 'pros-pro-m2')
    //->setHostname('{{INSERT_HOSTNAME_OR_IP_HERE}}')
    ->setPort('22')
    ->setConfigFile('~/.ssh/config')
    ->setRemoteUser('prospro')
    ->set('http_user', 'prospro')
    /**
     * Choose the restart command
     * for docker setup
     * 'docker restart {{application}}-app'
     *
     * for e.g. external servers use command from server maintainer e.g. for internex it looks like this
     * sudo /home/<USER>/scripts/restart-fpm.sh
     **/
    ->set('restart_command','/mr/restartsrv_apache_php_fpm')
    ->set('hyva_theme',[])
    ->set('docker',false)
    ->setDeployPath('{{deployment_root_path}}')
    ->setForwardAgent(true)
    ->setLabels([
        'env'  => ENV_PRODUCTION,
    ])
    ->set('branch', 'master');
